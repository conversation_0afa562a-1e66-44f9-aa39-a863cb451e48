<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notes API Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .api-endpoint {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            overflow: hidden;
        }
        
        .endpoint-header {
            background: #f5f5f5;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .method {
            padding: 5px 12px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .method.post { background: #28a745; color: white; }
        .method.get { background: #007bff; color: white; }
        .method.put { background: #ffc107; color: black; }
        .method.delete { background: #dc3545; color: white; }
        
        .endpoint-path {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #333;
        }
        
        .endpoint-body {
            padding: 15px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .response-example {
            background: #f0f8ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .feature-card h3 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .feature-card ul {
            list-style: none;
            padding-left: 0;
        }
        
        .feature-card li {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feature-card li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
        
        .demo-section {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin: 30px 0;
        }
        
        .demo-section h2 {
            border: none;
            color: white;
            margin-bottom: 15px;
        }
        
        .btn {
            background: white;
            color: #f5576c;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Notes API</h1>
            <p>Secure REST API with Go & Fiber Framework</p>
        </div>
        
        <div class="content">
            <div class="demo-section">
                <h2>🎯 Project Overview</h2>
                <p>A production-ready REST API built with Go and Fiber framework for secure user authentication and personal notes management. Features JWT authentication, comprehensive validation, and complete CRUD operations.</p>
                <button class="btn" onclick="scrollToSection('features')">View Features</button>
                <button class="btn" onclick="scrollToSection('api-docs')">API Documentation</button>
            </div>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">API Endpoints</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">85%</div>
                    <div class="stat-label">Test Coverage</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Test Cases</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Security Focus</div>
                </div>
            </div>
            
            <div id="features" class="section">
                <h2>🔥 Key Features</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>🔐 Authentication & Security</h3>
                        <ul>
                            <li>JWT token-based authentication</li>
                            <li>bcrypt password hashing (cost 12)</li>
                            <li>Input validation & sanitization</li>
                            <li>SQL injection protection</li>
                            <li>XSS prevention</li>
                            <li>CORS configuration</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>📝 Notes Management</h3>
                        <ul>
                            <li>Create, read, update, delete notes</li>
                            <li>User isolation (own notes only)</li>
                            <li>Pagination support</li>
                            <li>Search functionality</li>
                            <li>Title & content validation</li>
                            <li>Timestamps tracking</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>🏗️ Architecture</h3>
                        <ul>
                            <li>Clean Go project structure</li>
                            <li>Fiber v2 web framework</li>
                            <li>GORM v2 ORM with MySQL</li>
                            <li>Modular design patterns</li>
                            <li>Environment configuration</li>
                            <li>Docker containerization</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>🧪 Testing & Quality</h3>
                        <ul>
                            <li>Comprehensive unit tests</li>
                            <li>Integration test suite</li>
                            <li>API endpoint testing</li>
                            <li>Mock database testing</li>
                            <li>Coverage reporting</li>
                            <li>Error scenario testing</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div id="api-docs" class="section">
                <h2>📚 API Documentation</h2>
                
                <h3>Authentication Endpoints</h3>
                
                <div class="api-endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-path">/api/auth/register</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>Description:</strong> Register a new user account</p>
                        <p><strong>Request Body:</strong></p>
                        <div class="code-block">{
  "name": "John Doe",
  "email": "<EMAIL>", 
  "password": "password123"
}</div>
                        <div class="response-example">
                            <strong>Response (201):</strong>
                            <div class="code-block">{
  "message": "User created successfully",
  "data": { "user_id": 1 }
}</div>
                        </div>
                    </div>
                </div>
                
                <div class="api-endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-path">/api/auth/login</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>Description:</strong> Authenticate user and receive JWT token</p>
                        <p><strong>Request Body:</strong></p>
                        <div class="code-block">{
  "email": "<EMAIL>",
  "password": "password123"
}</div>
                        <div class="response-example">
                            <strong>Response (200):</strong>
                            <div class="code-block">{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": "24h",
  "user": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00Z"
  }
}</div>
                        </div>
                    </div>
                </div>
                
                <h3>Notes Endpoints (JWT Protected)</h3>
                
                <div class="api-endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-path">/api/notes</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>Description:</strong> Create a new note</p>
                        <p><strong>Headers:</strong> Authorization: Bearer &lt;jwt_token&gt;</p>
                        <div class="code-block">{
  "title": "My First Note",
  "content": "This is the content of my note."
}</div>
                    </div>
                </div>
                
                <div class="api-endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-path">/api/notes</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>Description:</strong> List user's notes with pagination and search</p>
                        <p><strong>Query Parameters:</strong> ?page=1&limit=10&search=keyword</p>
                        <div class="response-example">
                            <strong>Response:</strong>
                            <div class="code-block">{
  "notes": [...],
  "total": 25,
  "page": 1,
  "limit": 10
}</div>
                        </div>
                    </div>
                </div>
                
                <div class="api-endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-path">/api/notes/:id</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>Description:</strong> Get a specific note by ID</p>
                        <p><strong>Authorization:</strong> Must own the note</p>
                    </div>
                </div>
                
                <div class="api-endpoint">
                    <div class="endpoint-header">
                        <span class="method put">PUT</span>
                        <span class="endpoint-path">/api/notes/:id</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>Description:</strong> Update a specific note</p>
                        <p><strong>Note:</strong> Both title and content are optional</p>
                    </div>
                </div>
                
                <div class="api-endpoint">
                    <div class="endpoint-header">
                        <span class="method delete">DELETE</span>
                        <span class="endpoint-path">/api/notes/:id</span>
                    </div>
                    <div class="endpoint-body">
                        <p><strong>Description:</strong> Delete a specific note</p>
                        <p><strong>Response:</strong> 204 No Content</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🚀 How to Run the Project</h2>
                <div class="code-block">
# Using Docker (Recommended)
docker-compose up -d

# Or locally with Go
go mod download
go run cmd/main.go

# Run tests
./test.sh

# Test API endpoints
./test_api.sh
                </div>
            </div>
            
            <div class="section">
                <h2>📁 Project Structure</h2>
                <div class="code-block">
/
├── cmd/main.go                 # Application entry point
├── internal/
│   ├── models/                 # User and Note models
│   ├── handlers/               # Auth and Notes handlers  
│   ├── middleware/             # JWT and CORS middleware
│   ├── routes/                 # Route definitions
│   └── utils/                  # Utilities (JWT, validation)
├── config/database.go          # Database configuration
├── docker-compose.yml          # Docker services
├── Dockerfile                  # Multi-stage build
└── Tests and Documentation
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function scrollToSection(sectionId) {
            document.getElementById(sectionId).scrollIntoView({
                behavior: 'smooth'
            });
        }
        
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card, .api-endpoint');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
