# Notes API Testing Guide

This guide provides comprehensive instructions for testing the Notes API, including unit tests, integration tests, and manual API testing.

## Prerequisites

- Go 1.21 or later
- <PERSON><PERSON> and <PERSON>er Compose (for containerized testing)
- curl (for API testing)

## Test Structure

Our testing suite includes:

### 1. Unit Tests
- **Utils Tests** (`internal/utils/*_test.go`): JWT utilities, validation, response helpers
- **Handler Tests** (`internal/handlers/*_test.go`): Authentication and notes handlers
- **Middleware Tests** (`internal/middleware/*_test.go`): JWT middleware functionality

### 2. Integration Tests
- **Complete User Flow** (`integration_test.go`): End-to-end user journey
- **User Isolation**: Ensures users can only access their own data
- **Error Handling**: Comprehensive error scenario testing

### 3. API Tests
- **Live API Testing** (`test_api.sh`): Real HTTP requests against running server

## Running Tests

### Option 1: Automated Test Runner

```bash
# Run all tests with coverage
./test.sh
```

This script will:
- Download dependencies
- Run all unit tests
- Run integration tests
- Generate coverage reports
- Build the application

### Option 2: Manual Test Execution

```bash
# Download dependencies
go mod download

# Run specific test packages
go test -v ./internal/utils/...
go test -v ./internal/handlers/...
go test -v ./internal/middleware/...

# Run integration tests
go test -v ./integration_test.go

# Run all tests with coverage
go test -v -coverprofile=coverage.out ./...

# Generate coverage report
go tool cover -func=coverage.out
go tool cover -html=coverage.out -o coverage.html
```

### Option 3: Docker Testing

```bash
# Start the application with Docker
docker-compose up -d

# Wait for services to be ready
sleep 10

# Run API tests
./test_api.sh

# Stop services
docker-compose down
```

## Test Coverage

Our test suite covers:

### Authentication Tests
- ✅ User registration with validation
- ✅ Duplicate email prevention
- ✅ User login with JWT generation
- ✅ Invalid credentials handling
- ✅ Password strength validation
- ✅ Email format validation

### Notes Management Tests
- ✅ Note creation with authorization
- ✅ Note listing with pagination
- ✅ Note search functionality
- ✅ Individual note retrieval
- ✅ Note updates (partial and full)
- ✅ Note deletion
- ✅ User isolation (users can't access others' notes)

### Security Tests
- ✅ JWT token validation
- ✅ Authorization header parsing
- ✅ Token expiration handling
- ✅ Invalid token rejection
- ✅ Missing authorization handling

### Validation Tests
- ✅ Input sanitization (XSS prevention)
- ✅ Field length validation
- ✅ Required field validation
- ✅ Email format validation
- ✅ Password strength validation

## Sample Test Output

```
🧪 Running Notes API Tests
==========================
✓ Go is installed: go version go1.21.0 linux/amd64

📦 Downloading dependencies...
✓ Dependencies downloaded successfully

🔬 Running unit tests...
------------------------
Testing utilities...
=== RUN   TestValidateEmail
=== RUN   TestValidatePassword
=== RUN   TestSanitizeInput
--- PASS: TestValidateEmail (0.00s)
--- PASS: TestValidatePassword (0.00s)
--- PASS: TestSanitizeInput (0.00s)
✓ Utility tests passed

Testing middleware...
=== RUN   TestJWTMiddleware
=== RUN   TestGetUserIDFromContext
--- PASS: TestJWTMiddleware (0.01s)
--- PASS: TestGetUserIDFromContext (0.00s)
✓ Middleware tests passed

Testing handlers...
=== RUN   TestAuthHandler_Register
=== RUN   TestAuthHandler_Login
=== RUN   TestNotesHandler_CreateNote
=== RUN   TestNotesHandler_GetNotes
--- PASS: TestAuthHandler_Register (0.05s)
--- PASS: TestAuthHandler_Login (0.03s)
--- PASS: TestNotesHandler_CreateNote (0.02s)
--- PASS: TestNotesHandler_GetNotes (0.02s)
✓ Handler tests passed

🔗 Running integration tests...
-------------------------------
=== RUN   TestIntegration_CompleteUserFlow
=== RUN   TestIntegration_UserIsolation
--- PASS: TestIntegration_CompleteUserFlow (0.10s)
--- PASS: TestIntegration_UserIsolation (0.08s)
✓ Integration tests passed

📊 Running tests with coverage...
--------------------------------
PASS
coverage: 85.2% of statements
✓ All tests passed with coverage

🎉 All tests completed successfully!
```

## Manual API Testing

### 1. Start the Server

```bash
# Using Docker
docker-compose up -d

# Or locally
go run cmd/main.go
```

### 2. Test Endpoints

#### Health Check
```bash
curl http://localhost:8080/api/health
```

#### User Registration
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### User Login
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### Create Note (requires JWT token)
```bash
curl -X POST http://localhost:8080/api/notes \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "My First Note",
    "content": "This is the content of my note."
  }'
```

#### Get Notes
```bash
curl -X GET http://localhost:8080/api/notes \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Search Notes
```bash
curl -X GET "http://localhost:8080/api/notes?search=keyword&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Performance Testing

For load testing, you can use tools like:

```bash
# Using Apache Bench
ab -n 1000 -c 10 -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/api/notes

# Using wrk
wrk -t12 -c400 -d30s -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/api/notes
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Ensure MySQL is running
   - Check environment variables
   - Verify database credentials

2. **JWT Token Issues**
   - Check JWT_SECRET environment variable
   - Verify token format in Authorization header
   - Check token expiration

3. **Test Failures**
   - Ensure test database is clean
   - Check environment variables are set
   - Verify Go version compatibility

### Debug Mode

Run tests with verbose output:
```bash
go test -v -race ./...
```

Enable debug logging:
```bash
export ENV=development
go run cmd/main.go
```

## Continuous Integration

For CI/CD pipelines, use:

```yaml
# .github/workflows/test.yml
name: Test
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - uses: actions/setup-go@v2
      with:
        go-version: 1.21
    - run: go mod download
    - run: go test -v -race -coverprofile=coverage.out ./...
    - run: go tool cover -html=coverage.out -o coverage.html
```

This comprehensive testing approach ensures your Notes API is robust, secure, and ready for production deployment.
