# Notes API Documentation

## Overview

This document provides detailed information about the Notes API endpoints, request/response formats, and error handling.

## Base URL

```
http://localhost:8080/api
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. After logging in, include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## Error Response Format

All error responses follow this format:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

### Error Codes

- `VALIDATION_ERROR` - Input validation failed
- `UNAUTHORIZED` - Authentication required or invalid token
- `FORBIDDEN` - Access denied
- `NOT_FOUND` - Resource not found
- `CONFLICT` - Resource already exists
- `INTERNAL_SERVER_ERROR` - Server error

## Endpoints

### Health Check

#### GET /api/health

Check if the API is running.

**Response:**
```json
{
  "status": "ok",
  "message": "Notes API is running"
}
```

---

### Authentication

#### POST /api/auth/register

Register a new user account.

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Validation Rules:**
- `name`: Required, 2-100 characters
- `email`: Required, valid email format
- `password`: Required, minimum 8 characters, must contain at least one letter and one number

**Success Response (201):**
```json
{
  "message": "User created successfully",
  "data": {
    "user_id": 1
  }
}
```

**Error Responses:**
- `400` - Validation error
- `409` - Email already exists

#### POST /api/auth/login

Authenticate user and receive JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Success Response (200):**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": "24h",
  "user": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**Error Responses:**
- `400` - Invalid request body
- `401` - Invalid credentials

---

### Notes Management

All notes endpoints require authentication via JWT token.

#### POST /api/notes

Create a new note.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "My First Note",
  "content": "This is the content of my note."
}
```

**Validation Rules:**
- `title`: Required, maximum 200 characters
- `content`: Required

**Success Response (201):**
```json
{
  "id": 1,
  "title": "My First Note",
  "content": "This is the content of my note.",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### GET /api/notes

List user's notes with pagination and search.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `page` (optional): Page number, default 1
- `limit` (optional): Items per page, default 10, max 100
- `search` (optional): Search keyword for title and content

**Example:**
```
GET /api/notes?page=1&limit=10&search=important
```

**Success Response (200):**
```json
{
  "notes": [
    {
      "id": 1,
      "title": "Important Note",
      "content": "This is important content.",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

#### GET /api/notes/:id

Get a specific note by ID.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Success Response (200):**
```json
{
  "id": 1,
  "title": "My Note",
  "content": "Note content here.",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

**Error Responses:**
- `400` - Invalid note ID
- `404` - Note not found
- `403` - Note belongs to another user

#### PUT /api/notes/:id

Update a specific note.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "Updated Title",
  "content": "Updated content"
}
```

**Note:** Both fields are optional. Only provided fields will be updated.

**Success Response (200):**
```json
{
  "id": 1,
  "title": "Updated Title",
  "content": "Updated content",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T01:00:00Z"
}
```

#### DELETE /api/notes/:id

Delete a specific note.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Success Response (204):**
No content

**Error Responses:**
- `400` - Invalid note ID
- `404` - Note not found
- `403` - Note belongs to another user

## Rate Limiting

Currently, no rate limiting is implemented, but it's recommended to add rate limiting in production environments.

## CORS

CORS is configured to allow requests from origins specified in the `CORS_ORIGINS` environment variable.

## Security Considerations

1. Always use HTTPS in production
2. Keep JWT secrets secure and rotate them regularly
3. Implement rate limiting for authentication endpoints
4. Monitor for suspicious activity
5. Validate and sanitize all user inputs
6. Use strong passwords and consider implementing password policies
