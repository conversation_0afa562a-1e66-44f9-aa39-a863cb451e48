package models

import (
	"time"

	"gorm.io/gorm"
)

// Note represents the note model
type Note struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserID    uint           `json:"user_id" gorm:"not null;index"`
	Title     string         `json:"title" gorm:"not null;size:200"`
	Content   string         `json:"content" gorm:"type:text"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relationships
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// NoteCreateRequest represents the request body for creating a note
type NoteCreateRequest struct {
	Title   string `json:"title" validate:"required,max=200"`
	Content string `json:"content" validate:"required"`
}

// NoteUpdateRequest represents the request body for updating a note
type NoteUpdateRequest struct {
	Title   *string `json:"title,omitempty" validate:"omitempty,max=200"`
	Content *string `json:"content,omitempty"`
}

// NoteResponse represents the note response
type NoteResponse struct {
	ID        uint      `json:"id"`
	Title     string    `json:"title"`
	Content   string    `json:"content"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// NotesListResponse represents the paginated notes response
type NotesListResponse struct {
	Notes []NoteResponse `json:"notes"`
	Total int64          `json:"total"`
	Page  int            `json:"page"`
	Limit int            `json:"limit"`
}

// ToResponse converts Note to NoteResponse
func (n *Note) ToResponse() NoteResponse {
	return NoteResponse{
		ID:        n.ID,
		Title:     n.Title,
		Content:   n.Content,
		CreatedAt: n.CreatedAt,
		UpdatedAt: n.UpdatedAt,
	}
}

// ToResponseList converts slice of Notes to slice of NoteResponse
func ToResponseList(notes []Note) []NoteResponse {
	responses := make([]NoteResponse, len(notes))
	for i, note := range notes {
		responses[i] = note.ToResponse()
	}
	return responses
}
