package main

import (
	"github.com/gofiber/fiber/v2"
)

// Custom JWT Middleware
func JWTMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get Authorization header
		authHeader := c.Get("Authorization")

		// Extract token from header
		tokenString, err := ExtractTokenFromHeader(authHeader)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
				Error: err.Error(),
				Code:  "UNAUTHORIZED",
			})
		}

		// Validate token
		claims, err := ValidateJWT(tokenString)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
				Error: "Invalid or expired token",
				Code:  "UNAUTHORIZED",
			})
		}

		// Store user information in context
		c.Locals("userID", claims.UserID)
		c.Locals("userEmail", claims.Email)

		return c.Next()
	}
}

// Helper function to get user ID from context
func GetUserIDFromContext(c *fiber.Ctx) (uint, error) {
	userID, ok := c.Locals("userID").(uint)
	if !ok {
		return 0, fiber.NewError(fiber.StatusUnauthorized, "User ID not found in context")
	}
	return userID, nil
}

// Helper function to get user email from context
func GetUserEmailFromContext(c *fiber.Ctx) (string, error) {
	userEmail, ok := c.Locals("userEmail").(string)
	if !ok {
		return "", fiber.NewError(fiber.StatusUnauthorized, "User email not found in context")
	}
	return userEmail, nil
}
