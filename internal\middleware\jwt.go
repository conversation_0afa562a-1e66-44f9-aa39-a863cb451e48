package middleware

import (
	"notesapi/internal/utils"

	"github.com/gofiber/fiber/v2"
)

// JWTMiddleware validates JWT tokens and extracts user information
func JWTMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get Authorization header
		authHeader := c.Get("Authorization")
		
		// Extract token from header
		tokenString, err := utils.ExtractTokenFromHeader(authHeader)
		if err != nil {
			return utils.SendError(c, fiber.StatusUnauthorized, err.Error(), utils.ErrCodeUnauthorized)
		}

		// Validate token
		claims, err := utils.ValidateJWT(tokenString)
		if err != nil {
			return utils.SendError(c, fiber.StatusUnauthorized, "Invalid or expired token", utils.ErrCodeUnauthorized)
		}

		// Store user information in context
		c.Locals("userID", claims.UserID)
		c.Locals("userEmail", claims.Email)

		return c.Next()
	}
}

// GetUserIDFromContext extracts user ID from fiber context
func GetUserIDFromContext(c *fiber.Ctx) (uint, error) {
	userID, ok := c.Locals("userID").(uint)
	if !ok {
		return 0, fiber.NewError(fiber.StatusUnauthorized, "User ID not found in context")
	}
	return userID, nil
}

// GetUserEmailFromContext extracts user email from fiber context
func GetUserEmailFromContext(c *fiber.Ctx) (string, error) {
	userEmail, ok := c.Locals("userEmail").(string)
	if !ok {
		return "", fiber.NewError(fiber.StatusUnauthorized, "User email not found in context")
	}
	return userEmail, nil
}
