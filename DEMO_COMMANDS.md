# Notes API Demo Commands

This file contains example curl commands to demonstrate the Notes API functionality.

## Prerequisites

1. Start the API server:
   ```bash
   # Using Docker
   docker-compose up -d
   
   # OR locally
   go run cmd/main.go
   ```

2. Wait for the server to start (check health):
   ```bash
   curl http://localhost:8080/api/health
   ```

## Demo Flow

### 1. Health Check

```bash
curl -X GET http://localhost:8080/api/health
```

**Expected Response:**
```json
{
  "status": "ok",
  "message": "Notes API is running"
}
```

### 2. User Registration

```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Demo User",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

**Expected Response (201):**
```json
{
  "message": "User created successfully",
  "data": {
    "user_id": 1
  }
}
```

### 3. User Login

```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

**Expected Response (200):**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": "24h",
  "user": {
    "id": 1,
    "name": "Demo User",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**Save the token for subsequent requests:**
```bash
export TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 4. Create Notes

```bash
# Create first note
curl -X POST http://localhost:8080/api/notes \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "title": "My First Note",
    "content": "This is the content of my first note. It contains important information."
  }'

# Create second note
curl -X POST http://localhost:8080/api/notes \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "title": "Shopping List",
    "content": "1. Milk\n2. Bread\n3. Eggs\n4. Apples"
  }'

# Create third note
curl -X POST http://localhost:8080/api/notes \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "title": "Meeting Notes",
    "content": "Discussed project timeline and deliverables. Next meeting scheduled for Friday."
  }'
```

**Expected Response (201) for each:**
```json
{
  "id": 1,
  "title": "My First Note",
  "content": "This is the content of my first note. It contains important information.",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### 5. Get All Notes

```bash
curl -X GET http://localhost:8080/api/notes \
  -H "Authorization: Bearer $TOKEN"
```

**Expected Response (200):**
```json
{
  "notes": [
    {
      "id": 3,
      "title": "Meeting Notes",
      "content": "Discussed project timeline and deliverables. Next meeting scheduled for Friday.",
      "created_at": "2024-01-01T00:02:00Z",
      "updated_at": "2024-01-01T00:02:00Z"
    },
    {
      "id": 2,
      "title": "Shopping List",
      "content": "1. Milk\n2. Bread\n3. Eggs\n4. Apples",
      "created_at": "2024-01-01T00:01:00Z",
      "updated_at": "2024-01-01T00:01:00Z"
    },
    {
      "id": 1,
      "title": "My First Note",
      "content": "This is the content of my first note. It contains important information.",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 3,
  "page": 1,
  "limit": 10
}
```

### 6. Get Notes with Pagination

```bash
curl -X GET "http://localhost:8080/api/notes?page=1&limit=2" \
  -H "Authorization: Bearer $TOKEN"
```

### 7. Search Notes

```bash
curl -X GET "http://localhost:8080/api/notes?search=meeting" \
  -H "Authorization: Bearer $TOKEN"
```

**Expected Response (200):**
```json
{
  "notes": [
    {
      "id": 3,
      "title": "Meeting Notes",
      "content": "Discussed project timeline and deliverables. Next meeting scheduled for Friday.",
      "created_at": "2024-01-01T00:02:00Z",
      "updated_at": "2024-01-01T00:02:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

### 8. Get Specific Note

```bash
curl -X GET http://localhost:8080/api/notes/1 \
  -H "Authorization: Bearer $TOKEN"
```

**Expected Response (200):**
```json
{
  "id": 1,
  "title": "My First Note",
  "content": "This is the content of my first note. It contains important information.",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### 9. Update Note

```bash
curl -X PUT http://localhost:8080/api/notes/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "title": "My Updated First Note",
    "content": "This is the updated content of my first note. It now contains even more important information!"
  }'
```

**Expected Response (200):**
```json
{
  "id": 1,
  "title": "My Updated First Note",
  "content": "This is the updated content of my first note. It now contains even more important information!",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:05:00Z"
}
```

### 10. Partial Update (Only Title)

```bash
curl -X PUT http://localhost:8080/api/notes/2 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "title": "Updated Shopping List"
  }'
```

### 11. Delete Note

```bash
curl -X DELETE http://localhost:8080/api/notes/3 \
  -H "Authorization: Bearer $TOKEN"
```

**Expected Response (204):** No content

### 12. Verify Deletion

```bash
curl -X GET http://localhost:8080/api/notes \
  -H "Authorization: Bearer $TOKEN"
```

Should now show only 2 notes.

## Error Scenarios

### 1. Unauthorized Access

```bash
curl -X GET http://localhost:8080/api/notes
```

**Expected Response (401):**
```json
{
  "error": "authorization header is required",
  "code": "UNAUTHORIZED"
}
```

### 2. Invalid Credentials

```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "wrongpassword"
  }'
```

**Expected Response (401):**
```json
{
  "error": "Invalid credentials",
  "code": "UNAUTHORIZED"
}
```

### 3. Duplicate Registration

```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Another User",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

**Expected Response (409):**
```json
{
  "error": "User with this email already exists",
  "code": "CONFLICT"
}
```

### 4. Validation Errors

```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "",
    "email": "invalid-email",
    "password": "weak"
  }'
```

**Expected Response (400):**
```json
{
  "error": "Validation failed",
  "code": "VALIDATION_ERROR",
  "errors": [
    {
      "field": "name",
      "message": "name is required"
    },
    {
      "field": "email",
      "message": "Invalid email format"
    },
    {
      "field": "password",
      "message": "Password must be at least 8 characters long"
    }
  ]
}
```

### 5. Note Not Found

```bash
curl -X GET http://localhost:8080/api/notes/99999 \
  -H "Authorization: Bearer $TOKEN"
```

**Expected Response (404):**
```json
{
  "error": "Note not found",
  "code": "NOT_FOUND"
}
```

## Performance Testing

### Load Testing with curl

```bash
# Create multiple notes quickly
for i in {1..10}; do
  curl -X POST http://localhost:8080/api/notes \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d "{\"title\":\"Note $i\",\"content\":\"Content for note $i\"}" &
done
wait
```

### Concurrent User Simulation

```bash
# Simulate multiple users
for i in {1..5}; do
  (
    # Register user
    USER_EMAIL="user$<EMAIL>"
    curl -s -X POST http://localhost:8080/api/auth/register \
      -H "Content-Type: application/json" \
      -d "{\"name\":\"User $i\",\"email\":\"$USER_EMAIL\",\"password\":\"password123\"}"
    
    # Login user
    USER_TOKEN=$(curl -s -X POST http://localhost:8080/api/auth/login \
      -H "Content-Type: application/json" \
      -d "{\"email\":\"$USER_EMAIL\",\"password\":\"password123\"}" | \
      grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    
    # Create notes for user
    for j in {1..3}; do
      curl -s -X POST http://localhost:8080/api/notes \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $USER_TOKEN" \
        -d "{\"title\":\"User $i Note $j\",\"content\":\"Content from user $i note $j\"}"
    done
  ) &
done
wait
```

This demonstrates the complete functionality of the Notes API with real HTTP requests and expected responses.
