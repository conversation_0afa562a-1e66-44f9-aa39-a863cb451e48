package utils

import (
	"fmt"
	"regexp"
	"strings"
	"unicode"
)

// ValidateEmail validates email format using regex
func ValidateEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// ValidatePassword validates password strength
func ValidatePassword(password string) []string {
	var errors []string

	if len(password) < 8 {
		errors = append(errors, "Password must be at least 8 characters long")
	}

	hasLetter := false
	hasNumber := false

	for _, char := range password {
		if unicode.IsLetter(char) {
			hasLetter = true
		}
		if unicode.IsNumber(char) {
			hasNumber = true
		}
	}

	if !hasLetter {
		errors = append(errors, "Password must contain at least one letter")
	}

	if !hasNumber {
		errors = append(errors, "Password must contain at least one number")
	}

	return errors
}

// ValidateRequired checks if a field is required and not empty
func ValidateRequired(value string, fieldName string) *ValidationError {
	if strings.TrimSpace(value) == "" {
		return &ValidationError{
			Field:   fieldName,
			Message: fieldName + " is required",
		}
	}
	return nil
}

// ValidateMaxLength checks if a field exceeds maximum length
func ValidateMaxLength(value string, maxLength int, fieldName string) *ValidationError {
	if len(value) > maxLength {
		return &ValidationError{
			Field:   fieldName,
			Message: fieldName + " must not exceed " + fmt.Sprintf("%d", maxLength) + " characters",
		}
	}
	return nil
}

// ValidateMinLength checks if a field meets minimum length
func ValidateMinLength(value string, minLength int, fieldName string) *ValidationError {
	if len(value) < minLength {
		return &ValidationError{
			Field:   fieldName,
			Message: fieldName + " must be at least " + fmt.Sprintf("%d", minLength) + " characters long",
		}
	}
	return nil
}

// SanitizeInput removes potentially harmful characters for XSS prevention
func SanitizeInput(input string) string {
	// Remove HTML tags and script content
	htmlRegex := regexp.MustCompile(`<[^>]*>`)
	sanitized := htmlRegex.ReplaceAllString(input, "")

	// Remove script tags and their content
	scriptRegex := regexp.MustCompile(`(?i)<script[^>]*>.*?</script>`)
	sanitized = scriptRegex.ReplaceAllString(sanitized, "")

	// Trim whitespace
	sanitized = strings.TrimSpace(sanitized)

	return sanitized
}
