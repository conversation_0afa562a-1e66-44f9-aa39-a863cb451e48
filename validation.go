package main

import (
	"regexp"
	"strings"
	"unicode"
)

type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

type ValidationErrorResponse struct {
	Error  string            `json:"error"`
	Code   string            `json:"code"`
	Errors []ValidationError `json:"errors"`
}

func ValidateEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

func ValidatePassword(password string) []string {
	var errors []string

	if len(password) < 8 {
		errors = append(errors, "Password must be at least 8 characters long")
	}

	hasLetter := false
	hasNumber := false

	for _, char := range password {
		if unicode.IsLetter(char) {
			hasLetter = true
		}
		if unicode.IsNumber(char) {
			hasNumber = true
		}
	}

	if !hasLetter {
		errors = append(errors, "Password must contain at least one letter")
	}

	if !hasNumber {
		errors = append(errors, "Password must contain at least one number")
	}

	return errors
}

func ValidateRequired(value string, fieldName string) *ValidationError {
	if strings.TrimSpace(value) == "" {
		return &ValidationError{
			Field:   fieldName,
			Message: fieldName + " is required",
		}
	}
	return nil
}

func ValidateRegisterRequest(req RegisterRequest) []ValidationError {
	var errors []ValidationError

	// Validate name
	if err := ValidateRequired(req.Name, "name"); err != nil {
		errors = append(errors, *err)
	}

	// Validate email
	if err := ValidateRequired(req.Email, "email"); err != nil {
		errors = append(errors, *err)
	} else if !ValidateEmail(req.Email) {
		errors = append(errors, ValidationError{
			Field:   "email",
			Message: "Invalid email format",
		})
	}

	// Validate password
	if err := ValidateRequired(req.Password, "password"); err != nil {
		errors = append(errors, *err)
	} else {
		passwordErrors := ValidatePassword(req.Password)
		for _, errMsg := range passwordErrors {
			errors = append(errors, ValidationError{
				Field:   "password",
				Message: errMsg,
			})
		}
	}

	return errors
}

func ValidateLoginRequest(req LoginRequest) []ValidationError {
	var errors []ValidationError

	// Validate email
	if err := ValidateRequired(req.Email, "email"); err != nil {
		errors = append(errors, *err)
	} else if !ValidateEmail(req.Email) {
		errors = append(errors, ValidationError{
			Field:   "email",
			Message: "Invalid email format",
		})
	}

	// Validate password
	if err := ValidateRequired(req.Password, "password"); err != nil {
		errors = append(errors, *err)
	}

	return errors
}

func ValidateCreateNoteRequest(req CreateNoteRequest) []ValidationError {
	var errors []ValidationError

	// Validate title
	if err := ValidateRequired(req.Title, "title"); err != nil {
		errors = append(errors, *err)
	} else if len(req.Title) > 200 {
		errors = append(errors, ValidationError{
			Field:   "title",
			Message: "Title must not exceed 200 characters",
		})
	}

	// Validate content
	if err := ValidateRequired(req.Content, "content"); err != nil {
		errors = append(errors, *err)
	}

	return errors
}

func ValidateUpdateNoteRequest(req UpdateNoteRequest) []ValidationError {
	var errors []ValidationError

	// Validate title if provided
	if req.Title != nil && len(*req.Title) > 200 {
		errors = append(errors, ValidationError{
			Field:   "title",
			Message: "Title must not exceed 200 characters",
		})
	}

	return errors
}
