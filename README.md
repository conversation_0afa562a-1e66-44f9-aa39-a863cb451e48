# Notes API - Secure REST API with Go and Fiber

A secure REST API built with Go and the Fiber framework for user authentication and personal notes management. The API ensures that only authenticated users can access and manage their own notes with proper authorization checks.

## Features

- **User Authentication**: Registration and login with JWT tokens
- **Secure Notes Management**: CRUD operations for personal notes
- **Authorization**: Users can only access their own notes
- **Input Validation**: Comprehensive validation and sanitization
- **Password Security**: bcrypt hashing with cost factor 12
- **Search & Pagination**: Search notes with pagination support
- **Docker Support**: Containerized application with MySQL
- **CORS Support**: Configurable CORS settings

## Tech Stack

- **Backend**: Go 1.21+ with Fiber v2.x framework
- **Database**: MySQL 8.0+ with GORM v2 ORM
- **Authentication**: JWT using golang-jwt/jwt/v5
- **Password Hashing**: bcrypt
- **Containerization**: Docker and docker-compose
- **Environment**: godotenv for configuration

## Project Structure

```
/
├── cmd/
│   └── main.go                 # Application entry point
├── internal/
│   ├── models/                 # Database models
│   │   ├── user.go
│   │   └── note.go
│   ├── handlers/               # HTTP handlers
│   │   ├── auth.go
│   │   └── notes.go
│   ├── middleware/             # Custom middleware
│   │   ├── jwt.go
│   │   └── cors.go
│   ├── routes/                 # Route definitions
│   │   └── routes.go
│   └── utils/                  # Utility functions
│       ├── jwt.go
│       ├── validation.go
│       └── response.go
├── config/
│   └── database.go             # Database configuration
├── docker-compose.yml          # Docker services
├── Dockerfile                  # Go application container
├── init.sql                    # Database initialization
├── .env.example                # Environment template
├── go.mod                      # Go dependencies
└── README.md                   # This file
```

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Go 1.21+ (for local development)

### Using Docker (Recommended)

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd notesapi
   cp .env.example .env
   ```

2. **Start services**:
   ```bash
   docker-compose up -d
   ```

3. **Check health**:
   ```bash
   curl http://localhost:8080/api/health
   ```

### Local Development

1. **Install dependencies**:
   ```bash
   go mod download
   ```

2. **Setup environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

3. **Start MySQL** (using Docker):
   ```bash
   docker run -d --name mysql \
     -e MYSQL_ROOT_PASSWORD=password \
     -e MYSQL_DATABASE=notesapi \
     -p 3306:3306 mysql:8.0
   ```

4. **Run application**:
   ```bash
   go run cmd/main.go
   ```

## API Documentation

### Base URL
```
http://localhost:8080/api
```

### Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (201)**:
```json
{
  "message": "User created successfully",
  "data": {
    "user_id": 1
  }
}
```

#### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200)**:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": "24h",
  "user": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### Notes Endpoints (JWT Protected)

All notes endpoints require the `Authorization: Bearer <token>` header.

#### Create Note
```http
POST /api/notes
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "title": "My First Note",
  "content": "This is the content of my note."
}
```

#### List Notes (with pagination and search)
```http
GET /api/notes?page=1&limit=10&search=keyword
Authorization: Bearer <jwt_token>
```

#### Get Specific Note
```http
GET /api/notes/1
Authorization: Bearer <jwt_token>
```

#### Update Note
```http
PUT /api/notes/1
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "title": "Updated Title",
  "content": "Updated content"
}
```

#### Delete Note
```http
DELETE /api/notes/1
Authorization: Bearer <jwt_token>
```

## Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Database Configuration
DB_HOST=mysql
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=notesapi

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=8080
ENV=development

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
```

## Security Features

- **Password Hashing**: bcrypt with cost factor 12
- **JWT Authentication**: Secure token-based authentication
- **Input Validation**: Comprehensive validation for all inputs
- **SQL Injection Protection**: GORM parameterized queries
- **XSS Prevention**: Input sanitization
- **CORS Configuration**: Configurable cross-origin settings
- **Authorization**: Users can only access their own resources

## Testing

Run tests with:
```bash
go test ./...
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
