# Notes API Deployment Guide

This guide covers deploying the Notes API in various environments.

## Quick Start

### Using Docker (Recommended)

1. **Clone and setup**:
   ```bash
   git clone <your-repo>
   cd notesapi
   cp .env.example .env
   ```

2. **Configure environment**:
   Edit `.env` file with your settings:
   ```env
   DB_HOST=mysql
   DB_PORT=3306
   DB_USER=root
   DB_PASSWORD=your-secure-password
   DB_NAME=notesapi
   JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
   JWT_EXPIRES_IN=24h
   PORT=8080
   ENV=production
   ```

3. **Start services**:
   ```bash
   docker-compose up -d
   ```

4. **Verify deployment**:
   ```bash
   curl http://localhost:8080/api/health
   ```

### Local Development

1. **Install dependencies**:
   ```bash
   go mod download
   ```

2. **Setup MySQL**:
   ```bash
   # Using Docker
   docker run -d --name mysql \
     -e MYSQL_ROOT_PASSWORD=password \
     -e MYSQL_DATABASE=notesapi \
     -p 3306:3306 mysql:8.0
   ```

3. **Run application**:
   ```bash
   go run cmd/main.go
   ```

## Production Deployment

### Environment Variables

Set these environment variables in production:

```env
# Database
DB_HOST=your-mysql-host
DB_PORT=3306
DB_USER=notesapi_user
DB_PASSWORD=secure-database-password
DB_NAME=notesapi

# JWT
JWT_SECRET=very-long-random-secret-key-at-least-32-characters
JWT_EXPIRES_IN=24h

# Server
PORT=8080
ENV=production

# CORS
CORS_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
```

### Docker Production Setup

1. **Build production image**:
   ```bash
   docker build -t notesapi:latest .
   ```

2. **Run with production settings**:
   ```bash
   docker run -d \
     --name notesapi \
     -p 8080:8080 \
     -e DB_HOST=your-db-host \
     -e DB_PASSWORD=secure-password \
     -e JWT_SECRET=your-jwt-secret \
     -e ENV=production \
     notesapi:latest
   ```

### Kubernetes Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notesapi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: notesapi
  template:
    metadata:
      labels:
        app: notesapi
    spec:
      containers:
      - name: notesapi
        image: notesapi:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          value: "mysql-service"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: notesapi-secrets
              key: jwt-secret
---
apiVersion: v1
kind: Service
metadata:
  name: notesapi-service
spec:
  selector:
    app: notesapi
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

### Cloud Deployment

#### AWS ECS

```json
{
  "family": "notesapi",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "notesapi",
      "image": "your-account.dkr.ecr.region.amazonaws.com/notesapi:latest",
      "portMappings": [
        {
          "containerPort": 8080,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "JWT_SECRET",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:notesapi/jwt-secret"
        }
      ]
    }
  ]
}
```

#### Google Cloud Run

```bash
# Build and push to Google Container Registry
docker build -t gcr.io/your-project/notesapi .
docker push gcr.io/your-project/notesapi

# Deploy to Cloud Run
gcloud run deploy notesapi \
  --image gcr.io/your-project/notesapi \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars ENV=production \
  --set-env-vars PORT=8080
```

## Database Setup

### MySQL Production Configuration

```sql
-- Create database and user
CREATE DATABASE notesapi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'notesapi_user'@'%' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON notesapi.* TO 'notesapi_user'@'%';
FLUSH PRIVILEGES;

-- Optimize for production
SET GLOBAL innodb_buffer_pool_size = **********; -- 1GB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = ********; -- 64MB
```

### Database Migrations

The application automatically runs migrations on startup. For production, consider:

1. **Manual migrations**:
   ```bash
   # Run migrations separately
   go run cmd/migrate.go
   ```

2. **Backup before migrations**:
   ```bash
   mysqldump -u user -p notesapi > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

## Security Considerations

### Production Security Checklist

- [ ] Use strong JWT secrets (32+ characters)
- [ ] Enable HTTPS/TLS
- [ ] Configure CORS properly
- [ ] Use environment variables for secrets
- [ ] Enable database SSL connections
- [ ] Implement rate limiting
- [ ] Set up monitoring and logging
- [ ] Regular security updates
- [ ] Database connection pooling
- [ ] Input validation and sanitization

### HTTPS Setup

```nginx
# nginx.conf
server {
    listen 443 ssl;
    server_name api.yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Rate Limiting

Add to your Fiber app:

```go
import "github.com/gofiber/fiber/v2/middleware/limiter"

app.Use(limiter.New(limiter.Config{
    Max:        100,
    Expiration: 1 * time.Minute,
    KeyGenerator: func(c *fiber.Ctx) string {
        return c.Get("x-forwarded-for")
    },
}))
```

## Monitoring and Logging

### Health Checks

The API includes a health check endpoint:
```
GET /api/health
```

### Logging

Configure structured logging:

```go
import "github.com/gofiber/fiber/v2/middleware/logger"

app.Use(logger.New(logger.Config{
    Format: "${time} ${status} - ${method} ${path} - ${latency} - ${ip}\n",
    Output: os.Stdout,
}))
```

### Metrics

Consider adding Prometheus metrics:

```go
import "github.com/gofiber/fiber/v2/middleware/monitor"

app.Get("/metrics", monitor.New())
```

## Backup and Recovery

### Database Backup

```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u $DB_USER -p$DB_PASSWORD $DB_NAME > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
rm backup_$DATE.sql
```

### Application Backup

```bash
# Backup application files
tar -czf notesapi_backup_$(date +%Y%m%d).tar.gz \
  --exclude=node_modules \
  --exclude=.git \
  .
```

## Scaling

### Horizontal Scaling

The API is stateless and can be scaled horizontally:

```yaml
# docker-compose.scale.yml
version: '3.8'
services:
  app:
    image: notesapi:latest
    deploy:
      replicas: 3
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    depends_on:
      - app
```

### Load Balancing

```nginx
upstream notesapi {
    server app1:8080;
    server app2:8080;
    server app3:8080;
}

server {
    listen 80;
    location / {
        proxy_pass http://notesapi;
    }
}
```

## Troubleshooting

### Common Issues

1. **Database connection timeout**:
   - Check network connectivity
   - Verify credentials
   - Check firewall rules

2. **High memory usage**:
   - Monitor database connections
   - Check for memory leaks
   - Optimize queries

3. **Slow response times**:
   - Add database indexes
   - Enable query caching
   - Implement Redis caching

### Debug Commands

```bash
# Check application logs
docker logs notesapi

# Monitor resource usage
docker stats notesapi

# Database connection test
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD -e "SELECT 1"
```

This deployment guide ensures your Notes API runs reliably in production with proper security, monitoring, and scalability considerations.
