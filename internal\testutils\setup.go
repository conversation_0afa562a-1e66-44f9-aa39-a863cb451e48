package testutils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"notesapi/internal/models"
	"notesapi/internal/utils"

	"github.com/gofiber/fiber/v2"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// TestDB holds the test database instance
var TestDB *gorm.DB

// SetupTestDB initializes an in-memory SQLite database for testing
func SetupTestDB(t *testing.T) *gorm.DB {
	// Set test environment variables
	os.Setenv("JWT_SECRET", "test-secret-key")
	os.Setenv("JWT_EXPIRES_IN", "1h")

	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(&models.User{}, &models.Note{})
	if err != nil {
		t.Fatalf("Failed to migrate test database: %v", err)
	}

	TestDB = db
	return db
}

// CleanupTestDB cleans up the test database
func CleanupTestDB() {
	if TestDB != nil {
		sqlDB, _ := TestDB.DB()
		if sqlDB != nil {
			sqlDB.Close()
		}
	}
}

// CreateTestUser creates a test user in the database
func CreateTestUser(db *gorm.DB, name, email, password string) (*models.User, error) {
	user := &models.User{
		Name:     name,
		Email:    email,
		Password: password, // Will be hashed by BeforeCreate hook
	}

	if err := db.Create(user).Error; err != nil {
		return nil, err
	}

	return user, nil
}

// CreateTestNote creates a test note in the database
func CreateTestNote(db *gorm.DB, userID uint, title, content string) (*models.Note, error) {
	note := &models.Note{
		UserID:  userID,
		Title:   title,
		Content: content,
	}

	if err := db.Create(note).Error; err != nil {
		return nil, err
	}

	return note, nil
}

// GenerateTestJWT generates a JWT token for testing
func GenerateTestJWT(userID uint, email string) (string, error) {
	return utils.GenerateJWT(userID, email)
}

// MakeRequest makes an HTTP request to the Fiber app for testing
func MakeRequest(app *fiber.App, method, url string, body interface{}, headers map[string]string) (*httptest.ResponseRecorder, error) {
	var bodyReader io.Reader

	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		bodyReader = bytes.NewReader(jsonBody)
	}

	req := httptest.NewRequest(method, url, bodyReader)
	
	// Set default content type
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Set custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := app.Test(req, -1)
	if err != nil {
		return nil, err
	}

	recorder := httptest.NewRecorder()
	recorder.Code = resp.StatusCode
	recorder.Header().Set("Content-Type", resp.Header.Get("Content-Type"))
	
	// Copy response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	recorder.Body = bytes.NewBuffer(bodyBytes)

	return recorder, nil
}

// ParseJSONResponse parses JSON response body into a map
func ParseJSONResponse(resp *httptest.ResponseRecorder) (map[string]interface{}, error) {
	var result map[string]interface{}
	err := json.Unmarshal(resp.Body.Bytes(), &result)
	return result, err
}

// AssertStatusCode asserts that the response has the expected status code
func AssertStatusCode(t *testing.T, resp *httptest.ResponseRecorder, expected int) {
	if resp.Code != expected {
		t.Errorf("Expected status code %d, got %d. Response body: %s", expected, resp.Code, resp.Body.String())
	}
}

// AssertJSONField asserts that a JSON response contains a specific field with expected value
func AssertJSONField(t *testing.T, resp *httptest.ResponseRecorder, field string, expected interface{}) {
	data, err := ParseJSONResponse(resp)
	if err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	actual, exists := data[field]
	if !exists {
		t.Errorf("Field '%s' not found in response", field)
		return
	}

	if actual != expected {
		t.Errorf("Field '%s': expected %v, got %v", field, expected, actual)
	}
}

// AssertErrorResponse asserts that the response is an error with expected message
func AssertErrorResponse(t *testing.T, resp *httptest.ResponseRecorder, expectedStatus int, expectedError string) {
	AssertStatusCode(t, resp, expectedStatus)
	
	data, err := ParseJSONResponse(resp)
	if err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	errorMsg, exists := data["error"]
	if !exists {
		t.Error("Error field not found in response")
		return
	}

	if errorMsg != expectedError {
		t.Errorf("Expected error '%s', got '%s'", expectedError, errorMsg)
	}
}
