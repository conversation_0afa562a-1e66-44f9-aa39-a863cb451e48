package handlers

import (
	"notesapi/config"
	"notesapi/internal/models"
	"notesapi/internal/utils"
	"os"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	db *gorm.DB
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler() *AuthHandler {
	return &AuthHandler{
		db: config.GetDB(),
	}
}

// Register handles user registration
func (h *AuthHandler) Register(c *fiber.Ctx) error {
	var req models.UserRegistrationRequest
	
	// Parse request body
	if err := c.<PERSON>er(&req); err != nil {
		return utils.SendError(c, fiber.StatusBadRequest, "Invalid request body", utils.ErrCodeValidation)
	}

	// Validate input
	var validationErrors []utils.ValidationError

	// Validate name
	if err := utils.ValidateRequired(req.Name, "name"); err != nil {
		validationErrors = append(validationErrors, *err)
	} else if len(req.Name) < 2 || len(req.Name) > 100 {
		validationErrors = append(validationErrors, utils.ValidationError{
			Field:   "name",
			Message: "Name must be between 2 and 100 characters",
		})
	}

	// Validate email
	if err := utils.ValidateRequired(req.Email, "email"); err != nil {
		validationErrors = append(validationErrors, *err)
	} else if !utils.ValidateEmail(req.Email) {
		validationErrors = append(validationErrors, utils.ValidationError{
			Field:   "email",
			Message: "Invalid email format",
		})
	}

	// Validate password
	if err := utils.ValidateRequired(req.Password, "password"); err != nil {
		validationErrors = append(validationErrors, *err)
	} else {
		passwordErrors := utils.ValidatePassword(req.Password)
		for _, errMsg := range passwordErrors {
			validationErrors = append(validationErrors, utils.ValidationError{
				Field:   "password",
				Message: errMsg,
			})
		}
	}

	if len(validationErrors) > 0 {
		return utils.SendValidationError(c, validationErrors)
	}

	// Sanitize input
	req.Name = utils.SanitizeInput(req.Name)
	req.Email = utils.SanitizeInput(req.Email)

	// Check if user already exists
	var existingUser models.User
	if err := h.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return utils.SendError(c, fiber.StatusConflict, "User with this email already exists", utils.ErrCodeConflict)
	}

	// Create new user
	user := models.User{
		Name:     req.Name,
		Email:    req.Email,
		Password: req.Password, // Will be hashed by BeforeCreate hook
	}

	if err := h.db.Create(&user).Error; err != nil {
		return utils.SendError(c, fiber.StatusInternalServerError, "Failed to create user", utils.ErrCodeInternalServer)
	}

	// Return success response
	return utils.SendSuccess(c, fiber.StatusCreated, "User created successfully", fiber.Map{
		"user_id": user.ID,
	})
}

// Login handles user authentication
func (h *AuthHandler) Login(c *fiber.Ctx) error {
	var req models.UserLoginRequest
	
	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return utils.SendError(c, fiber.StatusBadRequest, "Invalid request body", utils.ErrCodeValidation)
	}

	// Validate input
	var validationErrors []utils.ValidationError

	if err := utils.ValidateRequired(req.Email, "email"); err != nil {
		validationErrors = append(validationErrors, *err)
	} else if !utils.ValidateEmail(req.Email) {
		validationErrors = append(validationErrors, utils.ValidationError{
			Field:   "email",
			Message: "Invalid email format",
		})
	}

	if err := utils.ValidateRequired(req.Password, "password"); err != nil {
		validationErrors = append(validationErrors, *err)
	}

	if len(validationErrors) > 0 {
		return utils.SendValidationError(c, validationErrors)
	}

	// Find user by email
	var user models.User
	if err := h.db.Where("email = ?", req.Email).First(&user).Error; err != nil {
		return utils.SendError(c, fiber.StatusUnauthorized, "Invalid credentials", utils.ErrCodeUnauthorized)
	}

	// Check password
	if !user.CheckPassword(req.Password) {
		return utils.SendError(c, fiber.StatusUnauthorized, "Invalid credentials", utils.ErrCodeUnauthorized)
	}

	// Generate JWT token
	token, err := utils.GenerateJWT(user.ID, user.Email)
	if err != nil {
		return utils.SendError(c, fiber.StatusInternalServerError, "Failed to generate token", utils.ErrCodeInternalServer)
	}

	// Get token expiration time
	expiresIn := os.Getenv("JWT_EXPIRES_IN")
	if expiresIn == "" {
		expiresIn = "24h"
	}

	// Return token
	return utils.SendData(c, fiber.StatusOK, fiber.Map{
		"token":      token,
		"expires_in": expiresIn,
		"user":       user.ToResponse(),
	})
}
