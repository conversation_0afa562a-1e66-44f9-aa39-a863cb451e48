package utils

import (
	"github.com/gofiber/fiber/v2"
)

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error string `json:"error"`
	Code  string `json:"code,omitempty"`
}

// SuccessResponse represents a success response
type SuccessResponse struct {
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// ValidationErrorResponse represents validation errors response
type ValidationErrorResponse struct {
	Error  string            `json:"error"`
	Code   string            `json:"code"`
	Errors []ValidationError `json:"errors"`
}

// SendError sends an error response
func SendError(c *fiber.Ctx, status int, message string, code ...string) error {
	errorCode := ""
	if len(code) > 0 {
		errorCode = code[0]
	}
	
	return c.Status(status).JSON(ErrorResponse{
		Error: message,
		Code:  errorCode,
	})
}

// SendSuccess sends a success response
func SendSuccess(c *fiber.Ctx, status int, message string, data ...interface{}) error {
	response := SuccessResponse{
		Message: message,
	}
	
	if len(data) > 0 {
		response.Data = data[0]
	}
	
	return c.Status(status).JSON(response)
}

// SendData sends data response without message
func SendData(c *fiber.Ctx, status int, data interface{}) error {
	return c.Status(status).JSON(data)
}

// SendValidationError sends validation error response
func SendValidationError(c *fiber.Ctx, errors []ValidationError) error {
	return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
		Error:  "Validation failed",
		Code:   "VALIDATION_ERROR",
		Errors: errors,
	})
}

// Common error codes
const (
	ErrCodeValidation     = "VALIDATION_ERROR"
	ErrCodeUnauthorized   = "UNAUTHORIZED"
	ErrCodeForbidden      = "FORBIDDEN"
	ErrCodeNotFound       = "NOT_FOUND"
	ErrCodeConflict       = "CONFLICT"
	ErrCodeInternalServer = "INTERNAL_SERVER_ERROR"
)
