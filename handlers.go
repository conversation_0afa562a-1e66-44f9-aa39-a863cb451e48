package main

import (
	"os"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// Health check handler
func HealthCheck(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"status":  "ok",
		"message": "Notes API is running",
	})
}

// Register handler
func Register(c *fiber.Ctx) error {
	var req RegisterRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
			Code:  "VALIDATION_ERROR",
		})
	}

	// Validate request
	if validationErrors := ValidateRegisterRequest(req); len(validationErrors) > 0 {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Code:   "VALIDATION_ERROR",
			Errors: validationErrors,
		})
	}

	// Check if user already exists
	var existingUser User
	if err := DB.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return c.Status(fiber.StatusConflict).JSON(ErrorResponse{
			Error: "User with this email already exists",
			Code:  "CONFLICT",
		})
	}

	// Create new user
	user := User{
		Name:     req.Name,
		Email:    req.Email,
		Password: req.Password, // Will be hashed by BeforeCreate hook
	}

	if err := DB.Create(&user).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to create user",
			Code:  "INTERNAL_SERVER_ERROR",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(SuccessResponse{
		Message: "User created successfully",
		Data:    fiber.Map{"user_id": user.ID},
	})
}

// Login handler
func Login(c *fiber.Ctx) error {
	var req LoginRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
			Code:  "VALIDATION_ERROR",
		})
	}

	// Validate request
	if validationErrors := ValidateLoginRequest(req); len(validationErrors) > 0 {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Code:   "VALIDATION_ERROR",
			Errors: validationErrors,
		})
	}

	// Find user by email
	var user User
	if err := DB.Where("email = ?", req.Email).First(&user).Error; err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "Invalid credentials",
			Code:  "UNAUTHORIZED",
		})
	}

	// Check password
	if !user.CheckPassword(req.Password) {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "Invalid credentials",
			Code:  "UNAUTHORIZED",
		})
	}

	// Generate JWT token
	token, err := GenerateJWT(user.ID, user.Email)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to generate token",
			Code:  "INTERNAL_SERVER_ERROR",
		})
	}

	// Get token expiration time
	expiresIn := os.Getenv("JWT_EXPIRES_IN")
	if expiresIn == "" {
		expiresIn = "24h"
	}

	return c.JSON(LoginResponse{
		Token:     token,
		ExpiresIn: expiresIn,
		User:      user.ToUserInfo(),
	})
}

// Create note handler
func CreateNote(c *fiber.Ctx) error {
	// Get user ID from context
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "Unauthorized",
			Code:  "UNAUTHORIZED",
		})
	}

	var req CreateNoteRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
			Code:  "VALIDATION_ERROR",
		})
	}

	// Validate request
	if validationErrors := ValidateCreateNoteRequest(req); len(validationErrors) > 0 {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Code:   "VALIDATION_ERROR",
			Errors: validationErrors,
		})
	}

	// Create note
	note := Note{
		UserID:  userID,
		Title:   req.Title,
		Content: req.Content,
	}

	if err := DB.Create(&note).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to create note",
			Code:  "INTERNAL_SERVER_ERROR",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(note)
}

// Get notes handler
func GetNotes(c *fiber.Ctx) error {
	// Get user ID from context
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "Unauthorized",
			Code:  "UNAUTHORIZED",
		})
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "10"))
	search := c.Query("search", "")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Build query
	query := DB.Where("user_id = ?", userID)

	// Add search functionality
	if search != "" {
		search = "%" + search + "%"
		query = query.Where("title LIKE ? OR content LIKE ?", search, search)
	}

	// Get total count
	var total int64
	if err := query.Model(&Note{}).Count(&total).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to count notes",
			Code:  "INTERNAL_SERVER_ERROR",
		})
	}

	// Get notes with pagination
	var notes []Note
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&notes).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to fetch notes",
			Code:  "INTERNAL_SERVER_ERROR",
		})
	}

	return c.JSON(NotesResponse{
		Notes: notes,
		Total: total,
		Page:  page,
		Limit: limit,
	})
}

// Get single note handler
func GetNote(c *fiber.Ctx) error {
	// Get user ID from context
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "Unauthorized",
			Code:  "UNAUTHORIZED",
		})
	}

	// Get note ID from URL parameter
	noteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid note ID",
			Code:  "VALIDATION_ERROR",
		})
	}

	// Find note
	var note Note
	if err := DB.Where("id = ? AND user_id = ?", noteID, userID).First(&note).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
				Error: "Note not found",
				Code:  "NOT_FOUND",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to fetch note",
			Code:  "INTERNAL_SERVER_ERROR",
		})
	}

	return c.JSON(note)
}

// Update note handler
func UpdateNote(c *fiber.Ctx) error {
	// Get user ID from context
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "Unauthorized",
			Code:  "UNAUTHORIZED",
		})
	}

	// Get note ID from URL parameter
	noteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid note ID",
			Code:  "VALIDATION_ERROR",
		})
	}

	var req UpdateNoteRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid request body",
			Code:  "VALIDATION_ERROR",
		})
	}

	// Validate request
	if validationErrors := ValidateUpdateNoteRequest(req); len(validationErrors) > 0 {
		return c.Status(fiber.StatusBadRequest).JSON(ValidationErrorResponse{
			Error:  "Validation failed",
			Code:   "VALIDATION_ERROR",
			Errors: validationErrors,
		})
	}

	// Find note and verify ownership
	var note Note
	if err := DB.Where("id = ? AND user_id = ?", noteID, userID).First(&note).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
				Error: "Note not found",
				Code:  "NOT_FOUND",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to fetch note",
			Code:  "INTERNAL_SERVER_ERROR",
		})
	}

	// Update fields if provided
	updates := make(map[string]interface{})

	if req.Title != nil {
		updates["title"] = *req.Title
	}

	if req.Content != nil {
		updates["content"] = *req.Content
	}

	// Perform update
	if len(updates) > 0 {
		if err := DB.Model(&note).Updates(updates).Error; err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
				Error: "Failed to update note",
				Code:  "INTERNAL_SERVER_ERROR",
			})
		}
	}

	// Fetch updated note
	if err := DB.Where("id = ?", noteID).First(&note).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to fetch updated note",
			Code:  "INTERNAL_SERVER_ERROR",
		})
	}

	return c.JSON(note)
}

// Delete note handler
func DeleteNote(c *fiber.Ctx) error {
	// Get user ID from context
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(ErrorResponse{
			Error: "Unauthorized",
			Code:  "UNAUTHORIZED",
		})
	}

	// Get note ID from URL parameter
	noteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Error: "Invalid note ID",
			Code:  "VALIDATION_ERROR",
		})
	}

	// Find note and verify ownership
	var note Note
	if err := DB.Where("id = ? AND user_id = ?", noteID, userID).First(&note).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(ErrorResponse{
				Error: "Note not found",
				Code:  "NOT_FOUND",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to fetch note",
			Code:  "INTERNAL_SERVER_ERROR",
		})
	}

	// Delete note
	if err := DB.Delete(&note).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Error: "Failed to delete note",
			Code:  "INTERNAL_SERVER_ERROR",
		})
	}

	return c.SendStatus(fiber.StatusNoContent)
}
