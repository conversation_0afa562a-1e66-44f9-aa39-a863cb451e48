package handlers

import (
	"fmt"
	"testing"

	"notesapi/internal/middleware"
	"notesapi/internal/models"
	"notesapi/internal/testutils"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func setupNotesTestApp(db *gorm.DB) *fiber.App {
	app := fiber.New()

	// Create handler with test database
	notesHandler := &NotesHandler{db: db}

	// Setup routes with JWT middleware
	notes := app.Group("/notes")
	notes.Use(middleware.JWTMiddleware())
	notes.Post("/", notesHandler.CreateNote)
	notes.Get("/", notesHandler.GetNotes)
	notes.Get("/:id", notesHandler.GetNote)
	notes.Put("/:id", notesHandler.UpdateNote)
	notes.Delete("/:id", notesHandler.DeleteNote)

	return app
}

func TestNotesHandler_CreateNote(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.CleanupTestDB()

	app := setupNotesTestApp(db)

	// Create test user
	testUser, err := testutils.CreateTestUser(db, "Test User", "<EMAIL>", "password123")
	if err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}

	// Generate JWT token
	token, err := testutils.GenerateTestJWT(testUser.ID, testUser.Email)
	if err != nil {
		t.Fatalf("Failed to generate test JWT: %v", err)
	}

	tests := []struct {
		name           string
		requestBody    interface{}
		headers        map[string]string
		expectedStatus int
		expectedError  string
	}{
		{
			name: "Valid note creation",
			requestBody: map[string]string{
				"title":   "Test Note",
				"content": "This is a test note content.",
			},
			headers: map[string]string{
				"Authorization": "Bearer " + token,
			},
			expectedStatus: 201,
		},
		{
			name: "Missing title",
			requestBody: map[string]string{
				"content": "This is a test note content.",
			},
			headers: map[string]string{
				"Authorization": "Bearer " + token,
			},
			expectedStatus: 400,
			expectedError:  "Validation failed",
		},
		{
			name: "Missing content",
			requestBody: map[string]string{
				"title": "Test Note",
			},
			headers: map[string]string{
				"Authorization": "Bearer " + token,
			},
			expectedStatus: 400,
			expectedError:  "Validation failed",
		},
		{
			name: "Title too long",
			requestBody: map[string]string{
				"title":   string(make([]byte, 201)), // 201 characters
				"content": "This is a test note content.",
			},
			headers: map[string]string{
				"Authorization": "Bearer " + token,
			},
			expectedStatus: 400,
			expectedError:  "Validation failed",
		},
		{
			name: "Missing authorization header",
			requestBody: map[string]string{
				"title":   "Test Note",
				"content": "This is a test note content.",
			},
			expectedStatus: 401,
			expectedError:  "authorization header is required",
		},
		{
			name: "Invalid token",
			requestBody: map[string]string{
				"title":   "Test Note",
				"content": "This is a test note content.",
			},
			headers: map[string]string{
				"Authorization": "Bearer invalid.token.here",
			},
			expectedStatus: 401,
			expectedError:  "Invalid or expired token",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := testutils.MakeRequest(app, "POST", "/notes/", tt.requestBody, tt.headers)
			if err != nil {
				t.Fatalf("Failed to make request: %v", err)
			}

			testutils.AssertStatusCode(t, resp, tt.expectedStatus)

			if tt.expectedError != "" {
				testutils.AssertErrorResponse(t, resp, tt.expectedStatus, tt.expectedError)
			} else if tt.expectedStatus == 201 {
				// Check success response
				data, err := testutils.ParseJSONResponse(resp)
				if err != nil {
					t.Fatalf("Failed to parse response: %v", err)
				}

				// Verify note data
				if data["id"] == nil {
					t.Error("Expected id in response")
				}
				if data["title"] != "Test Note" {
					t.Errorf("Expected title 'Test Note', got %v", data["title"])
				}
				if data["content"] != "This is a test note content." {
					t.Errorf("Expected content 'This is a test note content.', got %v", data["content"])
				}
			}
		})
	}
}

func TestNotesHandler_GetNotes(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.CleanupTestDB()

	app := setupNotesTestApp(db)

	// Create test users
	user1, err := testutils.CreateTestUser(db, "User 1", "<EMAIL>", "password123")
	if err != nil {
		t.Fatalf("Failed to create test user 1: %v", err)
	}

	user2, err := testutils.CreateTestUser(db, "User 2", "<EMAIL>", "password123")
	if err != nil {
		t.Fatalf("Failed to create test user 2: %v", err)
	}

	// Create test notes for user1
	_, err = testutils.CreateTestNote(db, user1.ID, "Note 1", "Content 1")
	if err != nil {
		t.Fatalf("Failed to create test note: %v", err)
	}
	_, err = testutils.CreateTestNote(db, user1.ID, "Note 2", "Content 2")
	if err != nil {
		t.Fatalf("Failed to create test note: %v", err)
	}

	// Create test note for user2 (should not be visible to user1)
	_, err = testutils.CreateTestNote(db, user2.ID, "User 2 Note", "User 2 Content")
	if err != nil {
		t.Fatalf("Failed to create test note: %v", err)
	}

	// Generate JWT token for user1
	token, err := testutils.GenerateTestJWT(user1.ID, user1.Email)
	if err != nil {
		t.Fatalf("Failed to generate test JWT: %v", err)
	}

	tests := []struct {
		name           string
		url            string
		headers        map[string]string
		expectedStatus int
		expectedCount  int
		expectedError  string
	}{
		{
			name: "Get all notes",
			url:  "/notes/",
			headers: map[string]string{
				"Authorization": "Bearer " + token,
			},
			expectedStatus: 200,
			expectedCount:  2, // Only user1's notes
		},
		{
			name: "Get notes with pagination",
			url:  "/notes/?page=1&limit=1",
			headers: map[string]string{
				"Authorization": "Bearer " + token,
			},
			expectedStatus: 200,
			expectedCount:  1,
		},
		{
			name: "Get notes with search",
			url:  "/notes/?search=Note 1",
			headers: map[string]string{
				"Authorization": "Bearer " + token,
			},
			expectedStatus: 200,
			expectedCount:  1,
		},
		{
			name:           "Missing authorization",
			url:            "/notes/",
			expectedStatus: 401,
			expectedError:  "authorization header is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := testutils.MakeRequest(app, "GET", tt.url, nil, tt.headers)
			if err != nil {
				t.Fatalf("Failed to make request: %v", err)
			}

			testutils.AssertStatusCode(t, resp, tt.expectedStatus)

			if tt.expectedError != "" {
				testutils.AssertErrorResponse(t, resp, tt.expectedStatus, tt.expectedError)
			} else if tt.expectedStatus == 200 {
				// Check success response
				data, err := testutils.ParseJSONResponse(resp)
				if err != nil {
					t.Fatalf("Failed to parse response: %v", err)
				}

				// Check notes array
				notes, ok := data["notes"].([]interface{})
				if !ok {
					t.Error("Expected notes array in response")
				} else if len(notes) != tt.expectedCount {
					t.Errorf("Expected %d notes, got %d", tt.expectedCount, len(notes))
				}

				// Check pagination data
				if data["total"] == nil {
					t.Error("Expected total in response")
				}
				if data["page"] == nil {
					t.Error("Expected page in response")
				}
				if data["limit"] == nil {
					t.Error("Expected limit in response")
				}
			}
		})
	}
}

func TestNotesHandler_GetNote(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.CleanupTestDB()

	app := setupNotesTestApp(db)

	// Create test users
	user1, err := testutils.CreateTestUser(db, "User 1", "<EMAIL>", "password123")
	if err != nil {
		t.Fatalf("Failed to create test user 1: %v", err)
	}

	user2, err := testutils.CreateTestUser(db, "User 2", "<EMAIL>", "password123")
	if err != nil {
		t.Fatalf("Failed to create test user 2: %v", err)
	}

	// Create test notes
	note1, err := testutils.CreateTestNote(db, user1.ID, "User 1 Note", "User 1 Content")
	if err != nil {
		t.Fatalf("Failed to create test note: %v", err)
	}

	note2, err := testutils.CreateTestNote(db, user2.ID, "User 2 Note", "User 2 Content")
	if err != nil {
		t.Fatalf("Failed to create test note: %v", err)
	}

	// Generate JWT tokens
	token1, err := testutils.GenerateTestJWT(user1.ID, user1.Email)
	if err != nil {
		t.Fatalf("Failed to generate test JWT: %v", err)
	}

	tests := []struct {
		name           string
		noteID         uint
		headers        map[string]string
		expectedStatus int
		expectedError  string
	}{
		{
			name:   "Get own note",
			noteID: note1.ID,
			headers: map[string]string{
				"Authorization": "Bearer " + token1,
			},
			expectedStatus: 200,
		},
		{
			name:   "Try to get another user's note",
			noteID: note2.ID,
			headers: map[string]string{
				"Authorization": "Bearer " + token1,
			},
			expectedStatus: 404,
			expectedError:  "Note not found",
		},
		{
			name:           "Missing authorization",
			noteID:         note1.ID,
			expectedStatus: 401,
			expectedError:  "authorization header is required",
		},
		{
			name: "Invalid note ID",
			headers: map[string]string{
				"Authorization": "Bearer " + token1,
			},
			expectedStatus: 400,
			expectedError:  "Invalid note ID",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			url := "/notes/"
			if tt.name != "Invalid note ID" {
				url = fmt.Sprintf("/notes/%d", tt.noteID)
			} else {
				url = "/notes/invalid"
			}

			resp, err := testutils.MakeRequest(app, "GET", url, nil, tt.headers)
			if err != nil {
				t.Fatalf("Failed to make request: %v", err)
			}

			testutils.AssertStatusCode(t, resp, tt.expectedStatus)

			if tt.expectedError != "" {
				testutils.AssertErrorResponse(t, resp, tt.expectedStatus, tt.expectedError)
			} else if tt.expectedStatus == 200 {
				// Check success response
				data, err := testutils.ParseJSONResponse(resp)
				if err != nil {
					t.Fatalf("Failed to parse response: %v", err)
				}

				// Verify note data
				if data["id"] != float64(note1.ID) {
					t.Errorf("Expected note ID %d, got %v", note1.ID, data["id"])
				}
				if data["title"] != note1.Title {
					t.Errorf("Expected title '%s', got %v", note1.Title, data["title"])
				}
			}
		})
	}
}

func TestNotesHandler_UpdateNote(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.CleanupTestDB()

	app := setupNotesTestApp(db)

	// Create test users
	user1, err := testutils.CreateTestUser(db, "User 1", "<EMAIL>", "password123")
	if err != nil {
		t.Fatalf("Failed to create test user 1: %v", err)
	}

	user2, err := testutils.CreateTestUser(db, "User 2", "<EMAIL>", "password123")
	if err != nil {
		t.Fatalf("Failed to create test user 2: %v", err)
	}

	// Create test notes
	note1, err := testutils.CreateTestNote(db, user1.ID, "Original Title", "Original Content")
	if err != nil {
		t.Fatalf("Failed to create test note: %v", err)
	}

	note2, err := testutils.CreateTestNote(db, user2.ID, "User 2 Note", "User 2 Content")
	if err != nil {
		t.Fatalf("Failed to create test note: %v", err)
	}

	// Generate JWT token
	token1, err := testutils.GenerateTestJWT(user1.ID, user1.Email)
	if err != nil {
		t.Fatalf("Failed to generate test JWT: %v", err)
	}

	tests := []struct {
		name           string
		noteID         uint
		requestBody    interface{}
		headers        map[string]string
		expectedStatus int
		expectedError  string
	}{
		{
			name:   "Update both title and content",
			noteID: note1.ID,
			requestBody: map[string]string{
				"title":   "Updated Title",
				"content": "Updated Content",
			},
			headers: map[string]string{
				"Authorization": "Bearer " + token1,
			},
			expectedStatus: 200,
		},
		{
			name:   "Try to update another user's note",
			noteID: note2.ID,
			requestBody: map[string]string{
				"title": "Hacked Title",
			},
			headers: map[string]string{
				"Authorization": "Bearer " + token1,
			},
			expectedStatus: 404,
			expectedError:  "Note not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			url := fmt.Sprintf("/notes/%d", tt.noteID)
			resp, err := testutils.MakeRequest(app, "PUT", url, tt.requestBody, tt.headers)
			if err != nil {
				t.Fatalf("Failed to make request: %v", err)
			}

			testutils.AssertStatusCode(t, resp, tt.expectedStatus)

			if tt.expectedError != "" {
				testutils.AssertErrorResponse(t, resp, tt.expectedStatus, tt.expectedError)
			}
		})
	}
}

func TestNotesHandler_DeleteNote(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.CleanupTestDB()

	app := setupNotesTestApp(db)

	// Create test users
	user1, err := testutils.CreateTestUser(db, "User 1", "<EMAIL>", "password123")
	if err != nil {
		t.Fatalf("Failed to create test user 1: %v", err)
	}

	user2, err := testutils.CreateTestUser(db, "User 2", "<EMAIL>", "password123")
	if err != nil {
		t.Fatalf("Failed to create test user 2: %v", err)
	}

	// Create test notes
	note1, err := testutils.CreateTestNote(db, user1.ID, "Note to Delete", "Content to Delete")
	if err != nil {
		t.Fatalf("Failed to create test note: %v", err)
	}

	note2, err := testutils.CreateTestNote(db, user2.ID, "User 2 Note", "User 2 Content")
	if err != nil {
		t.Fatalf("Failed to create test note: %v", err)
	}

	// Generate JWT token
	token1, err := testutils.GenerateTestJWT(user1.ID, user1.Email)
	if err != nil {
		t.Fatalf("Failed to generate test JWT: %v", err)
	}

	tests := []struct {
		name           string
		noteID         uint
		headers        map[string]string
		expectedStatus int
		expectedError  string
	}{
		{
			name:   "Delete own note",
			noteID: note1.ID,
			headers: map[string]string{
				"Authorization": "Bearer " + token1,
			},
			expectedStatus: 204,
		},
		{
			name:   "Try to delete another user's note",
			noteID: note2.ID,
			headers: map[string]string{
				"Authorization": "Bearer " + token1,
			},
			expectedStatus: 404,
			expectedError:  "Note not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			url := fmt.Sprintf("/notes/%d", tt.noteID)
			resp, err := testutils.MakeRequest(app, "DELETE", url, nil, tt.headers)
			if err != nil {
				t.Fatalf("Failed to make request: %v", err)
			}

			testutils.AssertStatusCode(t, resp, tt.expectedStatus)

			if tt.expectedError != "" {
				testutils.AssertErrorResponse(t, resp, tt.expectedStatus, tt.expectedError)
			} else if tt.expectedStatus == 204 {
				// Verify note was actually deleted
				var deletedNote models.Note
				err := db.Where("id = ?", tt.noteID).First(&deletedNote).Error
				if err == nil {
					t.Error("Note should have been deleted but still exists")
				}
			}
		})
	}
}