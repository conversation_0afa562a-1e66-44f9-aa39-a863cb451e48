package routes

import (
	"notesapi/internal/handlers"
	"notesapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// SetupRoutes configures all application routes
func SetupRoutes(app *fiber.App) {
	// Create handlers
	authHandler := handlers.NewAuthHandler()
	notesHandler := handlers.NewNotesHandler()

	// API group
	api := app.Group("/api")

	// Health check endpoint
	api.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":  "ok",
			"message": "Notes API is running",
		})
	})

	// Authentication routes
	auth := api.Group("/auth")
	auth.Post("/register", authHandler.Register)
	auth.Post("/login", authHandler.Login)

	// Protected notes routes
	notes := api.Group("/notes")
	notes.Use(middleware.JWTMiddleware()) // Apply JWT middleware to all notes routes

	// Notes CRUD operations
	notes.Post("/", notesHandler.CreateNote)
	notes.Get("/", notesHandler.GetNotes)
	notes.Get("/:id", notesHandler.GetNote)
	notes.Put("/:id", notesHandler.UpdateNote)
	notes.Delete("/:id", notesHandler.DeleteNote)
}
