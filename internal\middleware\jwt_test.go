package middleware

import (
	"testing"

	"notesapi/internal/testutils"
	"notesapi/internal/utils"

	"github.com/gofiber/fiber/v2"
)

func setupJWTTestApp() *fiber.App {
	app := fiber.New()
	
	// Protected route
	app.Get("/protected", JWTMiddleware(), func(c *fiber.Ctx) error {
		userID, err := GetUserIDFromContext(c)
		if err != nil {
			return c.Status(500).JSON(fiber.Map{"error": "Failed to get user ID"})
		}
		
		userEmail, err := GetUserEmailFromContext(c)
		if err != nil {
			return c.Status(500).JSON(fiber.Map{"error": "Failed to get user email"})
		}
		
		return c.JSON(fiber.Map{
			"user_id": userID,
			"email":   userEmail,
			"message": "Access granted",
		})
	})
	
	return app
}

func TestJWTMiddleware(t *testing.T) {
	testutils.SetupTestDB(t)
	defer testutils.CleanupTestDB()
	
	app := setupJWTTestApp()

	// Generate a valid token
	userID := uint(123)
	email := "<EMAIL>"
	validToken, err := utils.GenerateJWT(userID, email)
	if err != nil {
		t.Fatalf("Failed to generate test token: %v", err)
	}

	tests := []struct {
		name           string
		headers        map[string]string
		expectedStatus int
		expectedError  string
	}{
		{
			name: "Valid token",
			headers: map[string]string{
				"Authorization": "Bearer " + validToken,
			},
			expectedStatus: 200,
		},
		{
			name:           "Missing authorization header",
			headers:        map[string]string{},
			expectedStatus: 401,
			expectedError:  "authorization header is required",
		},
		{
			name: "Invalid token format",
			headers: map[string]string{
				"Authorization": "InvalidFormat",
			},
			expectedStatus: 401,
			expectedError:  "authorization header must start with 'Bearer '",
		},
		{
			name: "Invalid token",
			headers: map[string]string{
				"Authorization": "Bearer invalid.token.here",
			},
			expectedStatus: 401,
			expectedError:  "Invalid or expired token",
		},
		{
			name: "Empty token",
			headers: map[string]string{
				"Authorization": "Bearer ",
			},
			expectedStatus: 401,
			expectedError:  "token is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := testutils.MakeRequest(app, "GET", "/protected", nil, tt.headers)
			if err != nil {
				t.Fatalf("Failed to make request: %v", err)
			}

			testutils.AssertStatusCode(t, resp, tt.expectedStatus)

			if tt.expectedError != "" {
				testutils.AssertErrorResponse(t, resp, tt.expectedStatus, tt.expectedError)
			} else if tt.expectedStatus == 200 {
				// Check success response
				data, err := testutils.ParseJSONResponse(resp)
				if err != nil {
					t.Fatalf("Failed to parse response: %v", err)
				}

				// Verify user data
				if data["user_id"] != float64(userID) {
					t.Errorf("Expected user_id %d, got %v", userID, data["user_id"])
				}
				if data["email"] != email {
					t.Errorf("Expected email %s, got %v", email, data["email"])
				}
				if data["message"] != "Access granted" {
					t.Errorf("Expected message 'Access granted', got %v", data["message"])
				}
			}
		})
	}
}

func TestGetUserIDFromContext(t *testing.T) {
	app := fiber.New()
	
	app.Get("/test", func(c *fiber.Ctx) error {
		// Test without setting user ID
		_, err := GetUserIDFromContext(c)
		if err == nil {
			t.Error("Expected error when user ID not in context")
		}
		
		// Set user ID and test
		c.Locals("userID", uint(123))
		userID, err := GetUserIDFromContext(c)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}
		if userID != 123 {
			t.Errorf("Expected user ID 123, got %d", userID)
		}
		
		return c.SendStatus(200)
	})
	
	resp, err := testutils.MakeRequest(app, "GET", "/test", nil, nil)
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}
	
	testutils.AssertStatusCode(t, resp, 200)
}

func TestGetUserEmailFromContext(t *testing.T) {
	app := fiber.New()
	
	app.Get("/test", func(c *fiber.Ctx) error {
		// Test without setting user email
		_, err := GetUserEmailFromContext(c)
		if err == nil {
			t.Error("Expected error when user email not in context")
		}
		
		// Set user email and test
		c.Locals("userEmail", "<EMAIL>")
		userEmail, err := GetUserEmailFromContext(c)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}
		if userEmail != "<EMAIL>" {
			t.Errorf("Expected email '<EMAIL>', got %s", userEmail)
		}
		
		return c.SendStatus(200)
	})
	
	resp, err := testutils.MakeRequest(app, "GET", "/test", nil, nil)
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}
	
	testutils.AssertStatusCode(t, resp, 200)
}
