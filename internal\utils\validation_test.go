package utils

import (
	"testing"
)

func TestValidateEmail(t *testing.T) {
	tests := []struct {
		email    string
		expected bool
	}{
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"invalid-email", false},
		{"@domain.com", false},
		{"user@", false},
		{"", false},
	}

	for _, test := range tests {
		result := ValidateEmail(test.email)
		if result != test.expected {
			t.Errorf("ValidateEmail(%s) = %v; expected %v", test.email, result, test.expected)
		}
	}
}

func TestValidatePassword(t *testing.T) {
	tests := []struct {
		password string
		hasError bool
	}{
		{"password123", false}, // valid: has letter and number, 8+ chars
		{"12345678", true},     // invalid: no letters
		{"password", true},     // invalid: no numbers
		{"pass123", true},      // invalid: less than 8 chars
		{"", true},             // invalid: empty
	}

	for _, test := range tests {
		errors := ValidatePassword(test.password)
		hasError := len(errors) > 0
		if hasError != test.hasError {
			t.<PERSON><PERSON>("ValidatePassword(%s) hasError = %v; expected %v", test.password, hasError, test.hasError)
		}
	}
}

func TestSanitizeInput(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"<script>alert('xss')</script>", ""},
		{"Hello <b>World</b>", "Hello World"},
		{"  Normal text  ", "Normal text"},
		{"<p>Paragraph</p> content", "Paragraph content"},
	}

	for _, test := range tests {
		result := SanitizeInput(test.input)
		if result != test.expected {
			t.Errorf("SanitizeInput(%s) = %s; expected %s", test.input, result, test.expected)
		}
	}
}
