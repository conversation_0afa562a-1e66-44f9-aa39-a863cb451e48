#!/bin/bash

# Notes API Live Testing Script
# This script tests the API endpoints with real HTTP requests

set -e

# Configuration
API_BASE_URL="http://localhost:8080/api"
TEST_EMAIL="test$(date +%s)@example.com"  # Unique email for each test run
TEST_PASSWORD="password123"
TEST_NAME="Test User"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Function to make HTTP requests and check status
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local headers=$5
    
    print_info "Testing: $method $endpoint"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ ! -z "$headers" ]; then
        curl_cmd="$curl_cmd $headers"
    fi
    
    if [ ! -z "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd $API_BASE_URL$endpoint"
    
    local response=$(eval $curl_cmd)
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        print_status "Status: $status_code (Expected: $expected_status)"
        echo "$body"
        return 0
    else
        print_error "Status: $status_code (Expected: $expected_status)"
        echo "Response: $body"
        return 1
    fi
}

# Check if server is running
check_server() {
    print_info "Checking if server is running..."
    if curl -s "$API_BASE_URL/health" > /dev/null; then
        print_status "Server is running"
        return 0
    else
        print_error "Server is not running. Please start the server first:"
        echo "  docker-compose up -d"
        echo "  OR"
        echo "  go run cmd/main.go"
        exit 1
    fi
}

echo "🧪 Notes API Live Testing"
echo "========================="
echo ""

# Check server
check_server

echo ""
echo "🔍 Testing Health Check"
echo "----------------------"
make_request "GET" "/health" "" "200"

echo ""
echo "👤 Testing User Registration"
echo "----------------------------"
REGISTER_DATA="{\"name\":\"$TEST_NAME\",\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}"
make_request "POST" "/auth/register" "$REGISTER_DATA" "201"

echo ""
echo "🔐 Testing User Login"
echo "--------------------"
LOGIN_DATA="{\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}"
LOGIN_RESPONSE=$(make_request "POST" "/auth/login" "$LOGIN_DATA" "200")

# Extract token from login response
TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    print_error "Failed to extract token from login response"
    exit 1
fi

print_status "Token extracted successfully"
AUTH_HEADER="-H 'Authorization: Bearer $TOKEN'"

echo ""
echo "📝 Testing Note Creation"
echo "-----------------------"
NOTE_DATA="{\"title\":\"Test Note\",\"content\":\"This is a test note created via API testing.\"}"
NOTE_RESPONSE=$(make_request "POST" "/notes" "$NOTE_DATA" "201" "$AUTH_HEADER")

# Extract note ID
NOTE_ID=$(echo "$NOTE_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)

if [ -z "$NOTE_ID" ]; then
    print_error "Failed to extract note ID from response"
    exit 1
fi

print_status "Note created with ID: $NOTE_ID"

echo ""
echo "📋 Testing Get All Notes"
echo "-----------------------"
make_request "GET" "/notes" "" "200" "$AUTH_HEADER"

echo ""
echo "📄 Testing Get Specific Note"
echo "---------------------------"
make_request "GET" "/notes/$NOTE_ID" "" "200" "$AUTH_HEADER"

echo ""
echo "✏️ Testing Note Update"
echo "---------------------"
UPDATE_DATA="{\"title\":\"Updated Test Note\",\"content\":\"This note has been updated via API testing.\"}"
make_request "PUT" "/notes/$NOTE_ID" "$UPDATE_DATA" "200" "$AUTH_HEADER"

echo ""
echo "🔍 Testing Notes Search"
echo "----------------------"
make_request "GET" "/notes?search=Updated" "" "200" "$AUTH_HEADER"

echo ""
echo "📊 Testing Notes Pagination"
echo "--------------------------"
make_request "GET" "/notes?page=1&limit=5" "" "200" "$AUTH_HEADER"

echo ""
echo "🗑️ Testing Note Deletion"
echo "-----------------------"
make_request "DELETE" "/notes/$NOTE_ID" "" "204" "$AUTH_HEADER"

echo ""
echo "❌ Testing Error Cases"
echo "---------------------"

print_info "Testing unauthorized access..."
make_request "GET" "/notes" "" "401" || true

print_info "Testing invalid credentials..."
INVALID_LOGIN="{\"email\":\"$TEST_EMAIL\",\"password\":\"wrongpassword\"}"
make_request "POST" "/auth/login" "$INVALID_LOGIN" "401" || true

print_info "Testing duplicate registration..."
make_request "POST" "/auth/register" "$REGISTER_DATA" "409" || true

print_info "Testing invalid note ID..."
make_request "GET" "/notes/99999" "" "404" "$AUTH_HEADER" || true

echo ""
echo "🎉 API Testing Completed!"
echo "========================="
echo ""
echo "📋 Test Summary:"
echo "• Health check: ✓ Passed"
echo "• User registration: ✓ Passed"
echo "• User login: ✓ Passed"
echo "• Note creation: ✓ Passed"
echo "• Get notes: ✓ Passed"
echo "• Get specific note: ✓ Passed"
echo "• Update note: ✓ Passed"
echo "• Search notes: ✓ Passed"
echo "• Pagination: ✓ Passed"
echo "• Delete note: ✓ Passed"
echo "• Error handling: ✓ Passed"
echo ""
echo "🚀 Your Notes API is working perfectly!"
