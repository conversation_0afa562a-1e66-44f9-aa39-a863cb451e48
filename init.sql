-- Initialize database with proper charset and collation
CREATE DATABASE IF NOT EXISTS notesapi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE notesapi;

-- Create indexes for better performance
-- These will be created by GORM, but we can add them here for reference

-- Index on users.email for faster login queries
-- CREATE INDEX idx_users_email ON users(email);

-- Index on notes.user_id for faster user notes queries
-- CREATE INDEX idx_notes_user_id ON notes(user_id);

-- Full-text index on notes for search functionality
-- This will be added after GORM creates the tables
-- ALTER TABLE notes ADD FULLTEXT(title, content);
