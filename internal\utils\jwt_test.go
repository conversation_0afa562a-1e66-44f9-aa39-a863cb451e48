package utils

import (
	"os"
	"testing"
	"time"
)

func TestGenerateJWT(t *testing.T) {
	// Set test environment variables
	os.Setenv("JWT_SECRET", "test-secret-key")
	os.Setenv("JWT_EXPIRES_IN", "1h")

	tests := []struct {
		name    string
		userID  uint
		email   string
		wantErr bool
	}{
		{
			name:    "Valid user data",
			userID:  1,
			email:   "<EMAIL>",
			wantErr: false,
		},
		{
			name:    "Another valid user",
			userID:  123,
			email:   "<EMAIL>",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := GenerateJWT(tt.userID, tt.email)
			
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateJWT() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			
			if !tt.wantErr && token == "" {
				t.<PERSON>r("GenerateJWT() returned empty token")
			}
		})
	}
}

func TestGenerateJWTWithoutSecret(t *testing.T) {
	// Remove JWT_SECRET to test error case
	originalSecret := os.Getenv("JWT_SECRET")
	os.Unsetenv("JWT_SECRET")
	defer os.Setenv("JWT_SECRET", originalSecret)

	_, err := GenerateJWT(1, "<EMAIL>")
	if err == nil {
		t.Error("Expected error when JWT_SECRET is not set")
	}

	expectedError := "JWT_SECRET environment variable is not set"
	if err.Error() != expectedError {
		t.Errorf("Expected error '%s', got '%s'", expectedError, err.Error())
	}
}

func TestValidateJWT(t *testing.T) {
	// Set test environment variables
	os.Setenv("JWT_SECRET", "test-secret-key")
	os.Setenv("JWT_EXPIRES_IN", "1h")

	// Generate a valid token
	userID := uint(123)
	email := "<EMAIL>"
	token, err := GenerateJWT(userID, email)
	if err != nil {
		t.Fatalf("Failed to generate test token: %v", err)
	}

	tests := []struct {
		name      string
		token     string
		wantErr   bool
		wantID    uint
		wantEmail string
	}{
		{
			name:      "Valid token",
			token:     token,
			wantErr:   false,
			wantID:    userID,
			wantEmail: email,
		},
		{
			name:    "Invalid token",
			token:   "invalid.token.here",
			wantErr: true,
		},
		{
			name:    "Empty token",
			token:   "",
			wantErr: true,
		},
		{
			name:    "Malformed token",
			token:   "not.a.jwt",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			claims, err := ValidateJWT(tt.token)
			
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateJWT() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			
			if !tt.wantErr {
				if claims.UserID != tt.wantID {
					t.Errorf("Expected UserID %d, got %d", tt.wantID, claims.UserID)
				}
				if claims.Email != tt.wantEmail {
					t.Errorf("Expected Email %s, got %s", tt.wantEmail, claims.Email)
				}
			}
		})
	}
}

func TestValidateJWTWithoutSecret(t *testing.T) {
	// Remove JWT_SECRET to test error case
	originalSecret := os.Getenv("JWT_SECRET")
	os.Unsetenv("JWT_SECRET")
	defer os.Setenv("JWT_SECRET", originalSecret)

	_, err := ValidateJWT("some.token.here")
	if err == nil {
		t.Error("Expected error when JWT_SECRET is not set")
	}

	expectedError := "JWT_SECRET environment variable is not set"
	if err.Error() != expectedError {
		t.Errorf("Expected error '%s', got '%s'", expectedError, err.Error())
	}
}

func TestExtractTokenFromHeader(t *testing.T) {
	tests := []struct {
		name       string
		authHeader string
		wantToken  string
		wantErr    bool
	}{
		{
			name:       "Valid Bearer token",
			authHeader: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
			wantToken:  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
			wantErr:    false,
		},
		{
			name:       "Empty header",
			authHeader: "",
			wantErr:    true,
		},
		{
			name:       "Missing Bearer prefix",
			authHeader: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
			wantErr:    true,
		},
		{
			name:       "Bearer without token",
			authHeader: "Bearer ",
			wantErr:    true,
		},
		{
			name:       "Wrong prefix",
			authHeader: "Basic eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := ExtractTokenFromHeader(tt.authHeader)
			
			if (err != nil) != tt.wantErr {
				t.Errorf("ExtractTokenFromHeader() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			
			if !tt.wantErr && token != tt.wantToken {
				t.Errorf("Expected token '%s', got '%s'", tt.wantToken, token)
			}
		})
	}
}

func TestJWTExpiration(t *testing.T) {
	// Set test environment variables with short expiration
	os.Setenv("JWT_SECRET", "test-secret-key")
	os.Setenv("JWT_EXPIRES_IN", "1ms") // Very short expiration for testing

	token, err := GenerateJWT(1, "<EMAIL>")
	if err != nil {
		t.Fatalf("Failed to generate test token: %v", err)
	}

	// Wait for token to expire
	time.Sleep(10 * time.Millisecond)

	// Try to validate expired token
	_, err = ValidateJWT(token)
	if err == nil {
		t.Error("Expected error for expired token")
	}

	// Reset to normal expiration
	os.Setenv("JWT_EXPIRES_IN", "1h")
}
