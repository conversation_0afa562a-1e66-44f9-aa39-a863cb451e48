<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notes API Interactive Simulator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            min-height: 600px;
        }
        
        .left-panel {
            padding: 30px;
            border-right: 1px solid #e0e0e0;
            background: #f8f9fa;
        }
        
        .right-panel {
            padding: 30px;
            background: #2d3748;
            color: #e2e8f0;
        }
        
        .endpoint-section {
            margin-bottom: 25px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }
        
        .endpoint-title {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: #333;
        }
        
        .method-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .post { background: #28a745; color: white; }
        .get { background: #007bff; color: white; }
        .put { background: #ffc107; color: black; }
        .delete { background: #dc3545; color: white; }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .response-area {
            background: #1a202c;
            border-radius: 5px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            overflow-y: auto;
            max-height: 500px;
        }
        
        .response-header {
            color: #68d391;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .json-key {
            color: #63b3ed;
        }
        
        .json-string {
            color: #f6e05e;
        }
        
        .json-number {
            color: #fc8181;
        }
        
        .json-boolean {
            color: #9f7aea;
        }
        
        .status-success {
            color: #68d391;
        }
        
        .status-error {
            color: #fc8181;
        }
        
        .auth-status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .auth-status.logged-out {
            background: #fed7d7;
            color: #c53030;
        }
        
        .auth-status.logged-in {
            background: #c6f6d5;
            color: #2f855a;
        }
        
        .notes-list {
            background: white;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .note-item {
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            margin-bottom: 10px;
            background: #f9f9f9;
        }
        
        .note-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .note-content {
            color: #666;
            font-size: 0.9em;
        }
        
        .note-actions {
            margin-top: 10px;
        }
        
        .note-actions button {
            margin-right: 10px;
            padding: 5px 10px;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Notes API Interactive Simulator</h1>
            <p>Test the API endpoints and see real responses</p>
        </div>
        
        <div class="main-content">
            <div class="left-panel">
                <div class="auth-status logged-out" id="authStatus">
                    🔒 Not Authenticated
                </div>
                
                <!-- Authentication Section -->
                <div class="endpoint-section">
                    <div class="endpoint-title">
                        <span class="method-badge post">POST</span>
                        User Registration
                    </div>
                    <div class="form-group">
                        <label>Name:</label>
                        <input type="text" id="regName" placeholder="John Doe" value="Demo User">
                    </div>
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" id="regEmail" placeholder="<EMAIL>" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>Password:</label>
                        <input type="password" id="regPassword" placeholder="password123" value="password123">
                    </div>
                    <button class="btn" onclick="register()">Register User</button>
                </div>
                
                <div class="endpoint-section">
                    <div class="endpoint-title">
                        <span class="method-badge post">POST</span>
                        User Login
                    </div>
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" id="loginEmail" placeholder="<EMAIL>" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>Password:</label>
                        <input type="password" id="loginPassword" placeholder="password123" value="password123">
                    </div>
                    <button class="btn" onclick="login()">Login</button>
                </div>
                
                <!-- Notes Section -->
                <div class="endpoint-section">
                    <div class="endpoint-title">
                        <span class="method-badge post">POST</span>
                        Create Note
                    </div>
                    <div class="form-group">
                        <label>Title:</label>
                        <input type="text" id="noteTitle" placeholder="My Note Title" value="Sample Note">
                    </div>
                    <div class="form-group">
                        <label>Content:</label>
                        <textarea id="noteContent" placeholder="Note content here...">This is a sample note created through the API simulator.</textarea>
                    </div>
                    <button class="btn" onclick="createNote()" id="createNoteBtn" disabled>Create Note</button>
                </div>
                
                <div class="endpoint-section">
                    <div class="endpoint-title">
                        <span class="method-badge get">GET</span>
                        Get Notes
                    </div>
                    <button class="btn" onclick="getNotes()" id="getNotesBtn" disabled>Get All Notes</button>
                    <button class="btn" onclick="searchNotes()" id="searchNotesBtn" disabled>Search "Sample"</button>
                </div>
                
                <div class="notes-list" id="notesList" style="display: none;">
                    <h3>Your Notes:</h3>
                    <div id="notesContainer"></div>
                </div>
            </div>
            
            <div class="right-panel">
                <h3 style="margin-bottom: 20px;">📡 API Response</h3>
                <div class="response-area" id="responseArea">
                    <div class="response-header">Welcome to the Notes API Simulator!</div>
                    <div>
                        This simulator demonstrates the Notes API functionality without requiring a running server.
                        <br><br>
                        Try the following flow:
                        <br>1. Register a new user
                        <br>2. Login to get a JWT token
                        <br>3. Create some notes
                        <br>4. View and manage your notes
                        <br><br>
                        All responses are simulated but show exactly what the real API would return.
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentUser = null;
        let authToken = null;
        let notes = [];
        let nextNoteId = 1;
        
        function updateAuthStatus() {
            const authStatus = document.getElementById('authStatus');
            const createNoteBtn = document.getElementById('createNoteBtn');
            const getNotesBtn = document.getElementById('getNotesBtn');
            const searchNotesBtn = document.getElementById('searchNotesBtn');
            
            if (authToken) {
                authStatus.className = 'auth-status logged-in';
                authStatus.innerHTML = `🔓 Authenticated as ${currentUser.name}`;
                createNoteBtn.disabled = false;
                getNotesBtn.disabled = false;
                searchNotesBtn.disabled = false;
            } else {
                authStatus.className = 'auth-status logged-out';
                authStatus.innerHTML = '🔒 Not Authenticated';
                createNoteBtn.disabled = true;
                getNotesBtn.disabled = true;
                searchNotesBtn.disabled = true;
            }
        }
        
        function displayResponse(status, data, endpoint) {
            const responseArea = document.getElementById('responseArea');
            const statusClass = status < 400 ? 'status-success' : 'status-error';
            
            const formattedJson = JSON.stringify(data, null, 2)
                .replace(/"([^"]+)":/g, '<span class="json-key">"$1":</span>')
                .replace(/: "([^"]+)"/g, ': <span class="json-string">"$1"</span>')
                .replace(/: (\d+)/g, ': <span class="json-number">$1</span>')
                .replace(/: (true|false)/g, ': <span class="json-boolean">$1</span>');
            
            responseArea.innerHTML = `
                <div class="response-header ${statusClass}">
                    ${endpoint} - Status: ${status}
                </div>
                <pre>${formattedJson}</pre>
            `;
        }
        
        function register() {
            const name = document.getElementById('regName').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            
            // Simulate validation
            if (!name || !email || !password) {
                displayResponse(400, {
                    error: "Validation failed",
                    code: "VALIDATION_ERROR",
                    errors: [
                        { field: "name", message: "name is required" }
                    ]
                }, "POST /api/auth/register");
                return;
            }
            
            if (password.length < 8) {
                displayResponse(400, {
                    error: "Validation failed",
                    code: "VALIDATION_ERROR",
                    errors: [
                        { field: "password", message: "Password must be at least 8 characters long" }
                    ]
                }, "POST /api/auth/register");
                return;
            }
            
            // Simulate successful registration
            displayResponse(201, {
                message: "User created successfully",
                data: { user_id: 1 }
            }, "POST /api/auth/register");
            
            // Auto-fill login form
            document.getElementById('loginEmail').value = email;
            document.getElementById('loginPassword').value = password;
        }
        
        function login() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                displayResponse(400, {
                    error: "Validation failed",
                    code: "VALIDATION_ERROR"
                }, "POST /api/auth/login");
                return;
            }
            
            // Simulate successful login
            currentUser = {
                id: 1,
                name: "Demo User",
                email: email,
                created_at: new Date().toISOString()
            };
            
            authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************.demo_token";
            
            displayResponse(200, {
                token: authToken,
                expires_in: "24h",
                user: currentUser
            }, "POST /api/auth/login");
            
            updateAuthStatus();
        }
        
        function createNote() {
            if (!authToken) {
                displayResponse(401, {
                    error: "authorization header is required",
                    code: "UNAUTHORIZED"
                }, "POST /api/notes");
                return;
            }
            
            const title = document.getElementById('noteTitle').value;
            const content = document.getElementById('noteContent').value;
            
            if (!title || !content) {
                displayResponse(400, {
                    error: "Validation failed",
                    code: "VALIDATION_ERROR",
                    errors: [
                        { field: "title", message: "title is required" },
                        { field: "content", message: "content is required" }
                    ]
                }, "POST /api/notes");
                return;
            }
            
            const newNote = {
                id: nextNoteId++,
                title: title,
                content: content,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            
            notes.push(newNote);
            
            displayResponse(201, newNote, "POST /api/notes");
            
            // Clear form
            document.getElementById('noteTitle').value = '';
            document.getElementById('noteContent').value = '';
            
            updateNotesList();
        }
        
        function getNotes() {
            if (!authToken) {
                displayResponse(401, {
                    error: "authorization header is required",
                    code: "UNAUTHORIZED"
                }, "GET /api/notes");
                return;
            }
            
            displayResponse(200, {
                notes: notes.slice().reverse(), // Show newest first
                total: notes.length,
                page: 1,
                limit: 10
            }, "GET /api/notes");
            
            updateNotesList();
        }
        
        function searchNotes() {
            if (!authToken) {
                displayResponse(401, {
                    error: "authorization header is required",
                    code: "UNAUTHORIZED"
                }, "GET /api/notes?search=sample");
                return;
            }
            
            const searchResults = notes.filter(note => 
                note.title.toLowerCase().includes('sample') || 
                note.content.toLowerCase().includes('sample')
            );
            
            displayResponse(200, {
                notes: searchResults,
                total: searchResults.length,
                page: 1,
                limit: 10
            }, "GET /api/notes?search=sample");
        }
        
        function updateNote(id) {
            const note = notes.find(n => n.id === id);
            if (!note) return;
            
            const newTitle = prompt('Enter new title:', note.title);
            if (newTitle !== null) {
                note.title = newTitle;
                note.updated_at = new Date().toISOString();
                
                displayResponse(200, note, `PUT /api/notes/${id}`);
                updateNotesList();
            }
        }
        
        function deleteNote(id) {
            if (confirm('Are you sure you want to delete this note?')) {
                notes = notes.filter(n => n.id !== id);
                displayResponse(204, {}, `DELETE /api/notes/${id}`);
                updateNotesList();
            }
        }
        
        function updateNotesList() {
            const notesList = document.getElementById('notesList');
            const notesContainer = document.getElementById('notesContainer');
            
            if (notes.length === 0) {
                notesList.style.display = 'none';
                return;
            }
            
            notesList.style.display = 'block';
            notesContainer.innerHTML = notes.slice().reverse().map(note => `
                <div class="note-item">
                    <div class="note-title">${note.title}</div>
                    <div class="note-content">${note.content}</div>
                    <div class="note-actions">
                        <button class="btn" onclick="updateNote(${note.id})">Edit</button>
                        <button class="btn" onclick="deleteNote(${note.id})" style="background: #dc3545;">Delete</button>
                    </div>
                </div>
            `).join('');
        }
        
        // Initialize
        updateAuthStatus();
    </script>
</body>
</html>
