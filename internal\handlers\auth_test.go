package handlers

import (
	"testing"

	"notesapi/internal/models"
	"notesapi/internal/testutils"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func setupAuthTestApp(db *gorm.DB) *fiber.App {
	app := fiber.New()
	
	// Create handler with test database
	authHandler := &AuthHandler{db: db}
	
	// Setup routes
	app.Post("/register", authHandler.Register)
	app.Post("/login", authHandler.Login)
	
	return app
}

func TestAuthHandler_Register(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.CleanupTestDB()
	
	app := setupAuthTestApp(db)

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		expectedError  string
	}{
		{
			name: "Valid registration",
			requestBody: map[string]string{
				"name":     "<PERSON>",
				"email":    "<EMAIL>",
				"password": "password123",
			},
			expectedStatus: 201,
		},
		{
			name: "Missing name",
			requestBody: map[string]string{
				"email":    "<EMAIL>",
				"password": "password123",
			},
			expectedStatus: 400,
			expectedError:  "Validation failed",
		},
		{
			name: "Invalid email format",
			requestBody: map[string]string{
				"name":     "<PERSON> Doe",
				"email":    "invalid-email",
				"password": "password123",
			},
			expectedStatus: 400,
			expectedError:  "Validation failed",
		},
		{
			name: "Weak password",
			requestBody: map[string]string{
				"name":     "John Doe",
				"email":    "<EMAIL>",
				"password": "weak",
			},
			expectedStatus: 400,
			expectedError:  "Validation failed",
		},
		{
			name: "Password without numbers",
			requestBody: map[string]string{
				"name":     "John Doe",
				"email":    "<EMAIL>",
				"password": "passwordonly",
			},
			expectedStatus: 400,
			expectedError:  "Validation failed",
		},
		{
			name: "Password without letters",
			requestBody: map[string]string{
				"name":     "John Doe",
				"email":    "<EMAIL>",
				"password": "12345678",
			},
			expectedStatus: 400,
			expectedError:  "Validation failed",
		},
		{
			name:           "Invalid JSON",
			requestBody:    "invalid json",
			expectedStatus: 400,
			expectedError:  "Invalid request body",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := testutils.MakeRequest(app, "POST", "/register", tt.requestBody, nil)
			if err != nil {
				t.Fatalf("Failed to make request: %v", err)
			}

			testutils.AssertStatusCode(t, resp, tt.expectedStatus)

			if tt.expectedError != "" {
				testutils.AssertErrorResponse(t, resp, tt.expectedStatus, tt.expectedError)
			} else if tt.expectedStatus == 201 {
				// Check success response
				data, err := testutils.ParseJSONResponse(resp)
				if err != nil {
					t.Fatalf("Failed to parse response: %v", err)
				}

				if data["message"] != "User created successfully" {
					t.Errorf("Expected success message, got: %v", data["message"])
				}

				// Check that user_id is returned
				responseData, ok := data["data"].(map[string]interface{})
				if !ok {
					t.Error("Expected data field in response")
				} else if responseData["user_id"] == nil {
					t.Error("Expected user_id in response data")
				}
			}
		})
	}
}

func TestAuthHandler_RegisterDuplicateEmail(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.CleanupTestDB()
	
	app := setupAuthTestApp(db)

	// Create a user first
	_, err := testutils.CreateTestUser(db, "Existing User", "<EMAIL>", "password123")
	if err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}

	// Try to register with same email
	requestBody := map[string]string{
		"name":     "New User",
		"email":    "<EMAIL>",
		"password": "password123",
	}

	resp, err := testutils.MakeRequest(app, "POST", "/register", requestBody, nil)
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}

	testutils.AssertErrorResponse(t, resp, 409, "User with this email already exists")
}

func TestAuthHandler_Login(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.CleanupTestDB()
	
	app := setupAuthTestApp(db)

	// Create a test user
	testUser, err := testutils.CreateTestUser(db, "Test User", "<EMAIL>", "password123")
	if err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		expectedError  string
	}{
		{
			name: "Valid login",
			requestBody: map[string]string{
				"email":    "<EMAIL>",
				"password": "password123",
			},
			expectedStatus: 200,
		},
		{
			name: "Invalid email",
			requestBody: map[string]string{
				"email":    "<EMAIL>",
				"password": "password123",
			},
			expectedStatus: 401,
			expectedError:  "Invalid credentials",
		},
		{
			name: "Invalid password",
			requestBody: map[string]string{
				"email":    "<EMAIL>",
				"password": "wrongpassword",
			},
			expectedStatus: 401,
			expectedError:  "Invalid credentials",
		},
		{
			name: "Missing email",
			requestBody: map[string]string{
				"password": "password123",
			},
			expectedStatus: 400,
			expectedError:  "Validation failed",
		},
		{
			name: "Missing password",
			requestBody: map[string]string{
				"email": "<EMAIL>",
			},
			expectedStatus: 400,
			expectedError:  "Validation failed",
		},
		{
			name: "Invalid email format",
			requestBody: map[string]string{
				"email":    "invalid-email",
				"password": "password123",
			},
			expectedStatus: 400,
			expectedError:  "Validation failed",
		},
		{
			name:           "Invalid JSON",
			requestBody:    "invalid json",
			expectedStatus: 400,
			expectedError:  "Invalid request body",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := testutils.MakeRequest(app, "POST", "/login", tt.requestBody, nil)
			if err != nil {
				t.Fatalf("Failed to make request: %v", err)
			}

			testutils.AssertStatusCode(t, resp, tt.expectedStatus)

			if tt.expectedError != "" {
				testutils.AssertErrorResponse(t, resp, tt.expectedStatus, tt.expectedError)
			} else if tt.expectedStatus == 200 {
				// Check success response
				data, err := testutils.ParseJSONResponse(resp)
				if err != nil {
					t.Fatalf("Failed to parse response: %v", err)
				}

				// Check that token is returned
				if data["token"] == nil {
					t.Error("Expected token in response")
				}

				// Check that expires_in is returned
				if data["expires_in"] == nil {
					t.Error("Expected expires_in in response")
				}

				// Check user data
				userData, ok := data["user"].(map[string]interface{})
				if !ok {
					t.Error("Expected user data in response")
				} else {
					if userData["id"] != float64(testUser.ID) {
						t.Errorf("Expected user ID %d, got %v", testUser.ID, userData["id"])
					}
					if userData["email"] != testUser.Email {
						t.Errorf("Expected email %s, got %v", testUser.Email, userData["email"])
					}
					if userData["name"] != testUser.Name {
						t.Errorf("Expected name %s, got %v", testUser.Name, userData["name"])
					}
				}
			}
		})
	}
}
