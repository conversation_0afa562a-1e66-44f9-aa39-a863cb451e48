package handlers

import (
	"notesapi/config"
	"notesapi/internal/middleware"
	"notesapi/internal/models"
	"notesapi/internal/utils"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// NotesHandler handles notes-related requests
type NotesHandler struct {
	db *gorm.DB
}

// NewNotesHandler creates a new notes handler
func NewNotesHandler() *NotesHandler {
	return &NotesHandler{
		db: config.GetDB(),
	}
}

// CreateNote handles note creation
func (h *NotesHandler) CreateNote(c *fiber.Ctx) error {
	// Get user ID from context
	userID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return utils.SendError(c, fiber.StatusUnauthorized, "Unauthorized", utils.ErrCodeUnauthorized)
	}

	var req models.NoteCreateRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return utils.SendError(c, fiber.StatusBadRequest, "Invalid request body", utils.ErrCodeValidation)
	}

	// Validate input
	var validationErrors []utils.ValidationError

	if err := utils.ValidateRequired(req.Title, "title"); err != nil {
		validationErrors = append(validationErrors, *err)
	} else if len(req.Title) > 200 {
		validationErrors = append(validationErrors, utils.ValidationError{
			Field:   "title",
			Message: "Title must not exceed 200 characters",
		})
	}

	if err := utils.ValidateRequired(req.Content, "content"); err != nil {
		validationErrors = append(validationErrors, *err)
	}

	if len(validationErrors) > 0 {
		return utils.SendValidationError(c, validationErrors)
	}

	// Sanitize input
	req.Title = utils.SanitizeInput(req.Title)
	req.Content = utils.SanitizeInput(req.Content)

	// Create note
	note := models.Note{
		UserID:  userID,
		Title:   req.Title,
		Content: req.Content,
	}

	if err := h.db.Create(&note).Error; err != nil {
		return utils.SendError(c, fiber.StatusInternalServerError, "Failed to create note", utils.ErrCodeInternalServer)
	}

	return utils.SendData(c, fiber.StatusCreated, note.ToResponse())
}

// GetNotes handles listing user's notes with pagination and search
func (h *NotesHandler) GetNotes(c *fiber.Ctx) error {
	// Get user ID from context
	userID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return utils.SendError(c, fiber.StatusUnauthorized, "Unauthorized", utils.ErrCodeUnauthorized)
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "10"))
	search := c.Query("search", "")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Build query
	query := h.db.Where("user_id = ?", userID)

	// Add search functionality
	if search != "" {
		search = "%" + search + "%"
		query = query.Where("title LIKE ? OR content LIKE ?", search, search)
	}

	// Get total count
	var total int64
	if err := query.Model(&models.Note{}).Count(&total).Error; err != nil {
		return utils.SendError(c, fiber.StatusInternalServerError, "Failed to count notes", utils.ErrCodeInternalServer)
	}

	// Get notes with pagination
	var notes []models.Note
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&notes).Error; err != nil {
		return utils.SendError(c, fiber.StatusInternalServerError, "Failed to fetch notes", utils.ErrCodeInternalServer)
	}

	// Convert to response format
	response := models.NotesListResponse{
		Notes: models.ToResponseList(notes),
		Total: total,
		Page:  page,
		Limit: limit,
	}

	return utils.SendData(c, fiber.StatusOK, response)
}

// GetNote handles getting a specific note
func (h *NotesHandler) GetNote(c *fiber.Ctx) error {
	// Get user ID from context
	userID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return utils.SendError(c, fiber.StatusUnauthorized, "Unauthorized", utils.ErrCodeUnauthorized)
	}

	// Get note ID from URL parameter
	noteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return utils.SendError(c, fiber.StatusBadRequest, "Invalid note ID", utils.ErrCodeValidation)
	}

	// Find note
	var note models.Note
	if err := h.db.Where("id = ? AND user_id = ?", noteID, userID).First(&note).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.SendError(c, fiber.StatusNotFound, "Note not found", utils.ErrCodeNotFound)
		}
		return utils.SendError(c, fiber.StatusInternalServerError, "Failed to fetch note", utils.ErrCodeInternalServer)
	}

	return utils.SendData(c, fiber.StatusOK, note.ToResponse())
}

// UpdateNote handles updating a specific note
func (h *NotesHandler) UpdateNote(c *fiber.Ctx) error {
	// Get user ID from context
	userID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return utils.SendError(c, fiber.StatusUnauthorized, "Unauthorized", utils.ErrCodeUnauthorized)
	}

	// Get note ID from URL parameter
	noteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return utils.SendError(c, fiber.StatusBadRequest, "Invalid note ID", utils.ErrCodeValidation)
	}

	var req models.NoteUpdateRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return utils.SendError(c, fiber.StatusBadRequest, "Invalid request body", utils.ErrCodeValidation)
	}

	// Validate input
	var validationErrors []utils.ValidationError

	if req.Title != nil && len(*req.Title) > 200 {
		validationErrors = append(validationErrors, utils.ValidationError{
			Field:   "title",
			Message: "Title must not exceed 200 characters",
		})
	}

	if len(validationErrors) > 0 {
		return utils.SendValidationError(c, validationErrors)
	}

	// Find note and verify ownership
	var note models.Note
	if err := h.db.Where("id = ? AND user_id = ?", noteID, userID).First(&note).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.SendError(c, fiber.StatusNotFound, "Note not found", utils.ErrCodeNotFound)
		}
		return utils.SendError(c, fiber.StatusInternalServerError, "Failed to fetch note", utils.ErrCodeInternalServer)
	}

	// Update fields if provided
	updates := make(map[string]interface{})

	if req.Title != nil {
		sanitizedTitle := utils.SanitizeInput(*req.Title)
		updates["title"] = sanitizedTitle
	}

	if req.Content != nil {
		sanitizedContent := utils.SanitizeInput(*req.Content)
		updates["content"] = sanitizedContent
	}

	// Perform update
	if len(updates) > 0 {
		if err := h.db.Model(&note).Updates(updates).Error; err != nil {
			return utils.SendError(c, fiber.StatusInternalServerError, "Failed to update note", utils.ErrCodeInternalServer)
		}
	}

	// Fetch updated note
	if err := h.db.Where("id = ?", noteID).First(&note).Error; err != nil {
		return utils.SendError(c, fiber.StatusInternalServerError, "Failed to fetch updated note", utils.ErrCodeInternalServer)
	}

	return utils.SendData(c, fiber.StatusOK, note.ToResponse())
}

// DeleteNote handles deleting a specific note
func (h *NotesHandler) DeleteNote(c *fiber.Ctx) error {
	// Get user ID from context
	userID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return utils.SendError(c, fiber.StatusUnauthorized, "Unauthorized", utils.ErrCodeUnauthorized)
	}

	// Get note ID from URL parameter
	noteID, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return utils.SendError(c, fiber.StatusBadRequest, "Invalid note ID", utils.ErrCodeValidation)
	}

	// Find note and verify ownership
	var note models.Note
	if err := h.db.Where("id = ? AND user_id = ?", noteID, userID).First(&note).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.SendError(c, fiber.StatusNotFound, "Note not found", utils.ErrCodeNotFound)
		}
		return utils.SendError(c, fiber.StatusInternalServerError, "Failed to fetch note", utils.ErrCodeInternalServer)
	}

	// Delete note
	if err := h.db.Delete(&note).Error; err != nil {
		return utils.SendError(c, fiber.StatusInternalServerError, "Failed to delete note", utils.ErrCodeInternalServer)
	}

	return c.SendStatus(fiber.StatusNoContent)
}
