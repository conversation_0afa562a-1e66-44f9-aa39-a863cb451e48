version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: notes_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: notes_db
      MYSQL_USER: notes_user
      MYSQL_PASSWORD: notes_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - notes_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 40s

  # Go Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: notes_app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=password
      - DB_NAME=notes_db
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - JWT_EXPIRES_IN=24h
      - PORT=8080
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - notes_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/api/health"]
      timeout: 10s
      retries: 5
      interval: 30s
      start_period: 30s

volumes:
  mysql_data:
    driver: local

networks:
  notes_network:
    driver: bridge
