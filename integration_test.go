package main

import (
	"testing"

	"notesapi/internal/handlers"
	"notesapi/internal/middleware"
	"notesapi/internal/routes"
	"notesapi/internal/testutils"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
)

func setupIntegrationTestApp() *fiber.App {
	app := fiber.New(fiber.Config{
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			message := "Internal Server Error"

			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
				message = e.Message
			}

			return c.Status(code).JSON(fiber.Map{
				"error": message,
				"code":  "INTERNAL_SERVER_ERROR",
			})
		},
	})

	// Middleware
	app.Use(logger.New())
	app.Use(recover.New())
	app.Use(middleware.CORSMiddleware())

	// Setup routes
	routes.SetupRoutes(app)

	return app
}

func TestIntegration_CompleteUserFlow(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.CleanupTestDB()
	
	app := setupIntegrationTestApp()

	// Step 1: Register a new user
	registerData := map[string]string{
		"name":     "Integration Test User",
		"email":    "<EMAIL>",
		"password": "password123",
	}

	resp, err := testutils.MakeRequest(app, "POST", "/api/auth/register", registerData, nil)
	if err != nil {
		t.Fatalf("Failed to register user: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 201)

	// Step 2: Login with the registered user
	loginData := map[string]string{
		"email":    "<EMAIL>",
		"password": "password123",
	}

	resp, err = testutils.MakeRequest(app, "POST", "/api/auth/login", loginData, nil)
	if err != nil {
		t.Fatalf("Failed to login: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 200)

	// Extract token from login response
	loginResponse, err := testutils.ParseJSONResponse(resp)
	if err != nil {
		t.Fatalf("Failed to parse login response: %v", err)
	}

	token, ok := loginResponse["token"].(string)
	if !ok {
		t.Fatal("Token not found in login response")
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	// Step 3: Create a note
	noteData := map[string]string{
		"title":   "Integration Test Note",
		"content": "This is a test note created during integration testing.",
	}

	resp, err = testutils.MakeRequest(app, "POST", "/api/notes", noteData, headers)
	if err != nil {
		t.Fatalf("Failed to create note: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 201)

	// Extract note ID from response
	noteResponse, err := testutils.ParseJSONResponse(resp)
	if err != nil {
		t.Fatalf("Failed to parse note response: %v", err)
	}

	noteID := noteResponse["id"].(float64)

	// Step 4: Get all notes
	resp, err = testutils.MakeRequest(app, "GET", "/api/notes", nil, headers)
	if err != nil {
		t.Fatalf("Failed to get notes: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 200)

	notesResponse, err := testutils.ParseJSONResponse(resp)
	if err != nil {
		t.Fatalf("Failed to parse notes response: %v", err)
	}

	notes, ok := notesResponse["notes"].([]interface{})
	if !ok || len(notes) != 1 {
		t.Errorf("Expected 1 note, got %d", len(notes))
	}

	// Step 5: Get specific note
	resp, err = testutils.MakeRequest(app, "GET", "/api/notes/"+string(rune(int(noteID))), nil, headers)
	if err != nil {
		t.Fatalf("Failed to get specific note: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 200)

	// Step 6: Update the note
	updateData := map[string]string{
		"title":   "Updated Integration Test Note",
		"content": "This note has been updated during integration testing.",
	}

	resp, err = testutils.MakeRequest(app, "PUT", "/api/notes/"+string(rune(int(noteID))), updateData, headers)
	if err != nil {
		t.Fatalf("Failed to update note: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 200)

	// Step 7: Search notes
	resp, err = testutils.MakeRequest(app, "GET", "/api/notes?search=Updated", nil, headers)
	if err != nil {
		t.Fatalf("Failed to search notes: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 200)

	searchResponse, err := testutils.ParseJSONResponse(resp)
	if err != nil {
		t.Fatalf("Failed to parse search response: %v", err)
	}

	searchNotes, ok := searchResponse["notes"].([]interface{})
	if !ok || len(searchNotes) != 1 {
		t.Errorf("Expected 1 note in search results, got %d", len(searchNotes))
	}

	// Step 8: Delete the note
	resp, err = testutils.MakeRequest(app, "DELETE", "/api/notes/"+string(rune(int(noteID))), nil, headers)
	if err != nil {
		t.Fatalf("Failed to delete note: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 204)

	// Step 9: Verify note is deleted
	resp, err = testutils.MakeRequest(app, "GET", "/api/notes", nil, headers)
	if err != nil {
		t.Fatalf("Failed to get notes after deletion: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 200)

	finalResponse, err := testutils.ParseJSONResponse(resp)
	if err != nil {
		t.Fatalf("Failed to parse final response: %v", err)
	}

	finalNotes, ok := finalResponse["notes"].([]interface{})
	if !ok || len(finalNotes) != 0 {
		t.Errorf("Expected 0 notes after deletion, got %d", len(finalNotes))
	}
}

func TestIntegration_UserIsolation(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.CleanupTestDB()
	
	app := setupIntegrationTestApp()

	// Register two users
	user1Data := map[string]string{
		"name":     "User One",
		"email":    "<EMAIL>",
		"password": "password123",
	}

	user2Data := map[string]string{
		"name":     "User Two",
		"email":    "<EMAIL>",
		"password": "password123",
	}

	// Register users
	resp, err := testutils.MakeRequest(app, "POST", "/api/auth/register", user1Data, nil)
	if err != nil {
		t.Fatalf("Failed to register user 1: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 201)

	resp, err = testutils.MakeRequest(app, "POST", "/api/auth/register", user2Data, nil)
	if err != nil {
		t.Fatalf("Failed to register user 2: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 201)

	// Login both users
	resp, err = testutils.MakeRequest(app, "POST", "/api/auth/login", map[string]string{
		"email": "<EMAIL>", "password": "password123",
	}, nil)
	if err != nil {
		t.Fatalf("Failed to login user 1: %v", err)
	}
	user1Response, _ := testutils.ParseJSONResponse(resp)
	user1Token := user1Response["token"].(string)

	resp, err = testutils.MakeRequest(app, "POST", "/api/auth/login", map[string]string{
		"email": "<EMAIL>", "password": "password123",
	}, nil)
	if err != nil {
		t.Fatalf("Failed to login user 2: %v", err)
	}
	user2Response, _ := testutils.ParseJSONResponse(resp)
	user2Token := user2Response["token"].(string)

	// Create notes for each user
	user1Headers := map[string]string{"Authorization": "Bearer " + user1Token}
	user2Headers := map[string]string{"Authorization": "Bearer " + user2Token}

	resp, err = testutils.MakeRequest(app, "POST", "/api/notes", map[string]string{
		"title": "User 1 Note", "content": "User 1 Content",
	}, user1Headers)
	if err != nil {
		t.Fatalf("Failed to create note for user 1: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 201)

	resp, err = testutils.MakeRequest(app, "POST", "/api/notes", map[string]string{
		"title": "User 2 Note", "content": "User 2 Content",
	}, user2Headers)
	if err != nil {
		t.Fatalf("Failed to create note for user 2: %v", err)
	}
	testutils.AssertStatusCode(t, resp, 201)

	// Verify each user can only see their own notes
	resp, err = testutils.MakeRequest(app, "GET", "/api/notes", nil, user1Headers)
	if err != nil {
		t.Fatalf("Failed to get notes for user 1: %v", err)
	}
	user1Notes, _ := testutils.ParseJSONResponse(resp)
	notes1 := user1Notes["notes"].([]interface{})
	if len(notes1) != 1 {
		t.Errorf("User 1 should see 1 note, got %d", len(notes1))
	}

	resp, err = testutils.MakeRequest(app, "GET", "/api/notes", nil, user2Headers)
	if err != nil {
		t.Fatalf("Failed to get notes for user 2: %v", err)
	}
	user2Notes, _ := testutils.ParseJSONResponse(resp)
	notes2 := user2Notes["notes"].([]interface{})
	if len(notes2) != 1 {
		t.Errorf("User 2 should see 1 note, got %d", len(notes2))
	}
}

func TestIntegration_HealthCheck(t *testing.T) {
	app := setupIntegrationTestApp()

	resp, err := testutils.MakeRequest(app, "GET", "/api/health", nil, nil)
	if err != nil {
		t.Fatalf("Failed to check health: %v", err)
	}

	testutils.AssertStatusCode(t, resp, 200)
	testutils.AssertJSONField(t, resp, "status", "ok")
}
